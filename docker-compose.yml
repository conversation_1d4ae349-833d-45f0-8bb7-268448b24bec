version: "3"
services:
  water-back-v2:
    image: registry.gitlab.com/servisoft.uz/water-back-v2:latest
    container_name: water-back-v2
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - |
        python3 manage.py migrate &&
        python3 manage.py collectstatic --noinput &&
        python3 manage.py setup_webhook &&
        newrelic-admin run-program gunicorn --bind 0.0.0.0:8000 --access-logfile - config.wsgi:application
    ports:
      - "127.0.0.1:10007:8000"
    networks:
      - water-back-v2
    env_file:
      - .env

networks:
  water-back-v2:
    driver: bridge
