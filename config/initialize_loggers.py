import os
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


LOGGER_NAMES = [
    'django',
    'django.request',  # Add this to catch request-related logs
    'django.server',   # Add this to capture request/response details
    'django.db.backends',  # Add this to see database queries
    'users',
    'celery',
    'tg_bot'
]

LOG_LEVELS = [
    "debug",
    "info",
    "warning",
    "error",
    "critical"
]


def check_directories(base_dir):
    LOGS_ROOT = f'{base_dir}/logs'
    if not os.path.exists(LOGS_ROOT):
        os.makedirs(LOGS_ROOT)
    for app_name in LOGGER_NAMES:
        app_dir = f'{LOGS_ROOT}/{app_name}'
        if not os.path.exists(app_dir):
            os.makedirs(app_dir)


def _get_logging(base_dir):
    check_directories(base_dir)

    # Create file handlers
    handlers = {}
    for level in LOG_LEVELS:
        for name in LOGGER_NAMES:
            handler_name = f'{name}_{level}_handler'
            handlers.update({
                handler_name: {
                    'class': 'logging.FileHandler',
                    'level': f'{level.upper()}',
                    'filename': f'{base_dir}/logs/{name}/{level}.log',
                    'formatter': 'default',
                }
            })
    
    # Add console handlers for different types of logs
    handlers.update({
        'request_console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'request_formatter',
        }
    })

    # Build logger configurations with both file handlers and console handler
    loggers = {}
    for name in LOGGER_NAMES:
        handlers_list = [f'{name}_{level}_handler' for level in LOG_LEVELS]
        
        # Add appropriate console handler based on logger type
        if name in ['django.request', 'django.server']:
            handlers_list.append('request_console')  # Use request-specific console formatter
        else:
            handlers_list.append('console')  # Use standard console formatter
            
        loggers[name] = {
            'handlers': handlers_list,
            'level': 'DEBUG',
            'propagate': False,
        }

    LOGGING = {
        'version': 1,
        'disable_existing_loggers': False,
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
            },
        },
        'loggers': {
            'django.server': {
                'handlers': ['console'],
                'level': 'INFO',
                'propagate': False,
            },
        },
    }


class CustomErrorHandlerMiddleware(MiddlewareMixin):
    def process_exception(self, request, exception):
        """
        Catch unhandled exceptions and return a structured JSON response.
        """
        response_data = {
            "error": "Internal Server Error",
            "message": str(exception),
        }
        return JsonResponse(response_data, status=500)
