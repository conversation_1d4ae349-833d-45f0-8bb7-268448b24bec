from datetime import timed<PERSON>ta

from pathlib import Path
import dj_database_url
from environs import Env

from configurations import Configuration
from config.initialize_loggers import _get_logging
from corsheaders.defaults import default_headers

# Initialize Env
env = Env()
env.read_env()  # Read the .env file

BASE_DIR = Path(__file__).resolve().parent.parent.parent


class Common(Configuration):
    DEBUG = env.bool('DJANGO_DEBUG', default=False)

    INSTALLED_APPS = (
        'unfold',
        'unfold.contrib.filters',
        'unfold.contrib.forms',
        'django.contrib.admin',
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'django.contrib.staticfiles',
        'rest_framework',
        'rest_framework.authtoken',
        'django_filters',
        'drf_yasg',
        'smart_selects',
        'corsheaders',
        'storages',
        'modeltranslation',
        'apps.users',
        'apps.consultation',
        'apps.helpers',
        'apps.products',
        'apps.orders',
        'apps.users.vendors',
        'apps.users.couriers',
        'apps.bot',
        'apps.notifications'  # Added notifications app
    )

    MIDDLEWARE = (
        'django.middleware.security.SecurityMiddleware',
        'django.contrib.sessions.middleware.SessionMiddleware',
        'corsheaders.middleware.CorsMiddleware',
        'django.middleware.common.CommonMiddleware',
        'django.middleware.csrf.CsrfViewMiddleware',
        'django.contrib.auth.middleware.AuthenticationMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
        'django.middleware.clickjacking.XFrameOptionsMiddleware',
        'config.initialize_loggers.CustomErrorHandlerMiddleware',  # Custom middleware for handling errors
    )

    # Allow all hosts in development, including ngrok domains
    ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["*", "*.ngrok-free.app", "*.ngrok.io"])

    ROOT_URLCONF = 'config.urls'
    SECRET_KEY = env.str('DJANGO_SECRET_KEY')
    WSGI_APPLICATION = 'config.wsgi.application'

    # Email settings
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
    ADMINS = (
        ('Author', '<EMAIL>'),
    )

    # Postgres setup
    db_config = dj_database_url.config(
        default=env.str('DATABASE_URL'),
        conn_max_age=600
    )
    db_config.update({
        'OPTIONS': {
            'options': '-c timezone=Asia/Tashkent',
            'client_encoding': 'UTF8'
        },
        'TIME_ZONE': 'Asia/Tashkent',
    })
    DATABASES = {
        'default': db_config
    }

    LANGUAGES = (
        ('uz', 'Uzbek'),
        ('ru', 'Russian'),
        ('en', 'English'),
    )

    MODELTRANSLATION_DEFAULT_LANGUAGE = 'uz'
    MODELTRANSLATION_LANGUAGES = ('uz', 'ru', 'en')
    MODELTRANSLATION_FALLBACK_LANGUAGES = ('uz', )

    # General settings
    APPEND_SLASH = True
    TIME_ZONE = 'Asia/Tashkent'
    USE_TZ = True
    LANGUAGE_CODE = 'en-us'
    USE_I18N = False
    USE_L10N = True

    # Static files
    STATIC_ROOT = BASE_DIR / 'static'
    STATICFILES_DIRS = []
    STATIC_URL = '/static/'

    TEMPLATES = [
        {
            'BACKEND': 'django.template.backends.django.DjangoTemplates',
            'DIRS': STATICFILES_DIRS,
            'APP_DIRS': True,
            'OPTIONS': {
                'context_processors': [
                    'django.template.context_processors.debug',
                    'django.template.context_processors.request',
                    'django.contrib.auth.context_processors.auth',
                    'django.contrib.messages.context_processors.messages',
                ],
            },
        },
    ]

    # Password validation
    AUTH_PASSWORD_VALIDATORS = [
        {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
        {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
        {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
        {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
    ]

    # Logging
    LOGGING = _get_logging(BASE_DIR)
    AUTH_USER_MODEL = 'users.User'

    # Django Rest Framework configuration
    REST_FRAMEWORK = {
        'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
        'DEFAULT_PAGINATION_CLASS': 'core.paginations.CustomPagination',
        'DEFAULT_AUTHENTICATION_CLASSES': (
            "rest_framework_simplejwt.authentication.JWTAuthentication",
            'rest_framework.authentication.BasicAuthentication',
            'rest_framework.authentication.SessionAuthentication',
        ),
    }

    AUTHENTICATION_BACKENDS = [
        'apps.users.backends.CustomAuthenticationBackend',
        'django.contrib.auth.backends.ModelBackend',
    ]

    SIMPLE_JWT = {
        'ACCESS_TOKEN_LIFETIME': timedelta(days=5),
        'REFRESH_TOKEN_LIFETIME': timedelta(days=15),
        'ROTATE_REFRESH_TOKENS': True,
        'BLACKLIST_AFTER_ROTATION': True,
        'UPDATE_LAST_LOGIN': True,
        'ALGORITHM': 'HS256',
        'VERIFYING_KEY': None,
        'AUDIENCE': None,
        'ISSUER': None,
        'JWK_URL': None,
        'LEEWAY': 0,
    }

    # Session settings
    SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
    SESSION_CACHE_ALIAS = 'default'
    SESSION_SAVE_EVERY_REQUEST = True

    CORS_ORIGIN_ALLOW_ALL = True
    CORS_ALLOW_CREDENTIALS = True
    CSRF_TRUSTED_ORIGINS = env.list("CSRF_TRUSTED_ORIGINS", default=[
        "https://suvol.uz",
        "https://api.suvol.uz",
        "https://*.ngrok-free.app",
        "https://*.ngrok.io",
    ])
    CORS_ALLOW_HEADERS = [
        'authorization',
        'content-type',
        'x-requested-with',
        'upgrade-insecure-requests',
        'accept',
        'origin',
        'user-agent',
        'cache-control',
        'x-csrftoken',
        'x-initiator',
        *default_headers,
    ]


    # SMS and MinIO configuration
    ESKIZ_EMAIL = env.str('ESKIZ_EMAIL')
    ESKIZ_PASSWORD = env.str('ESKIZ_PASSWORD')

    MINIO_ENDPOINT = env.str("MINIO_ENDPOINT")
    MINIO_ACCESS_KEY = env.str('MINIO_ACCESS_KEY')
    MINIO_BUCKET_NAME = env.str('MINIO_BUCKET_NAME')
    MINIO_SECRET_KEY = env.str('MINIO_SECRET_KEY')
    MINIO_SECURE = env.bool("MINIO_SECURE", default=False)

    # DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
    AWS_ACCESS_KEY_ID = MINIO_ACCESS_KEY
    AWS_SECRET_ACCESS_KEY = MINIO_SECRET_KEY
    AWS_STORAGE_BUCKET_NAME = MINIO_BUCKET_NAME
    AWS_S3_ENDPOINT_URL = MINIO_ENDPOINT
    AWS_QUERYSTRING_AUTH = env.bool("MINIO_QUERYSTRING_AUTH", default=True)
    AWS_S3_SECURE_URLS = env.bool("MINIO_SECURE", default=False)

    # Storage configuration
    STORAGES = {
        "default": {
            "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        },
        "staticfiles": {
            "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
            "OPTIONS": {
                "location": "static"
            }
        }
    }

    MEDIA_URL = f"{MINIO_ENDPOINT}/{MINIO_BUCKET_NAME}/"
    MEDIA_ROOT = "/tmp/media"

    BOT_TOKEN = env.str("BOT_TOKEN")
    MINI_APP_URL = env.str("MINI_APP_URL")
    TELEGRAM_WEBHOOK_URL = env.str("TELEGRAM_WEBHOOK_URL", "https://api.suvol.uz")

    # Courier bot settings
    COURIER_BOT_TOKEN = env.str("COURIER_BOT_TOKEN")
    COURIER_APP_URL = env.str("COURIER_APP_URL")

    # Unfold settings
    UNFOLD = {
        "SITE_TITLE": "Water Back Admin",
        "SITE_HEADER": "Water Back Administration",
        "SITE_URL": "/",
        "SITE_ICON": None,
        "DASHBOARD_CALLBACK": None,
        "LOGIN": {
            "image": None,
            "redirect_after": None,
        },
        "STYLES": [
            lambda request: "/static/css/unfold.css",
        ],
        "SCRIPTS": [
            lambda request: "/static/js/unfold.js",
        ],
    }

    # Firebase configuration
    FIREBASE_PROJECT_ID = env.str('FIREBASE_PROJECT_ID')
    FIREBASE_PRIVATE_KEY_ID = env.str('FIREBASE_PRIVATE_KEY_ID')
    FIREBASE_PRIVATE_KEY = env.str('FIREBASE_PRIVATE_KEY')
    FIREBASE_CLIENT_EMAIL = env.str('FIREBASE_CLIENT_EMAIL')
    FIREBASE_CLIENT_ID = env.str('FIREBASE_CLIENT_ID')
    FIREBASE_AUTH_URI = env.str('FIREBASE_AUTH_URI')
    FIREBASE_TOKEN_URI = env.str('FIREBASE_TOKEN_URI')
    FIREBASE_AUTH_PROVIDER_CERT_URL = env.str('FIREBASE_AUTH_PROVIDER_CERT_URL')
    FIREBASE_CLIENT_CERT_URL = env.str('FIREBASE_CLIENT_CERT_URL')
    FIREBASE_DATABASE_URL = env.str('FIREBASE_DATABASE_URL')
