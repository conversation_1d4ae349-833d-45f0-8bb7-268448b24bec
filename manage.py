#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys
import dotenv

CONFIGURATION = os.getenv("DJANGO_CONFIGURATION", "Local")

def main():
    # Load environment variables
    dotenv.load_dotenv(dotenv.find_dotenv(), override=True)
    
    # Set Django settings module
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.config")
    os.environ.setdefault("DJANGO_CONFIGURATION", CONFIGURATION)

    try:
        # Import django-configurations management command execution
        from configurations.management import execute_from_command_line
        
        # Execute the command
        execute_from_command_line(sys.argv)
    except ImportError as exc:
        try:
            import django  # noqa
        except ImportError:
            raise ImportError(
                "Couldn't import Django. Are you sure it's installed and "
                "available on your PYTHONPATH environment variable? Did you "
                "forget to activate a virtual environment?"
            ) from exc
        raise


if __name__ == '__main__':
    main()
