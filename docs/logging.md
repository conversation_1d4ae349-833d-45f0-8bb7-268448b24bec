## Where can I see the logger names?
[LOGGER NAMES](config/initialize_loggers.py)

## How to use logger?
```python
import logging

logger = logging.getLogger('logger name')

logger.info('The process has started!')

try:
    driver = webdriver.Firefox(service=..., options=...)
    element = driver.find_element(...)
except ConnectionError as e:
    logger.warning(e)
except ElementNotFoundError as e:
    logger.error(e)
except Exception as e:
    logger.critical(e)

logger.info('The process is complete!')
```

## How can I view the logs on the server?
```bash
cd /project_path
sudo tail -100f logs/<loggername>/debug.log
```

## How can I add more loggers?
~~~
add the new logger name to the "LOGGER_NAMES" list
config/initialize_loggers.py > LOGGER_NAMES

Keep in mind!
When adding a new logger, the runserver command must be restarted

python manage.py runserver
~~~
