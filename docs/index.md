# obihayot

[![Build Status](https://travis-ci.org/sevbo2003/obihayot.svg?branch=master)](https://travis-ci.org/sevbo2003/obihayot)
[![Built with](https://img.shields.io/badge/Built_with-Cookiecutter_Django_Rest-F7B633.svg)](https://github.com/agconti/cookiecutter-django-rest)

Obihayot crm project. Check out the project's [documentation](http://sevbo2003.github.io/obihayot/).

# Prerequisites

- [Docker](https://docs.docker.com/docker-for-mac/install/)

# Initialize the project

Start the dev server for local development:

```bash
docker-compose up
```

Create a superuser to login to the admin:

```bash
docker-compose run --rm web ./manage.py createsuperuser
```
