from django.core.management.base import BaseCommand
from apps.users.models import User, Customer, UserTypes


class Command(BaseCommand):
    help = "Create missing Customer instances for Users with user_type CUSTOMER"

    def handle(self, *args, **options):
        users_without_customer = User.objects.filter(
            user_type=UserTypes.CUSTOMER
        ).exclude(
            id__in=Customer.objects.values_list('user_id', flat=True)
        )
        count = users_without_customer.count()
        self.stdout.write(f"Found {count} users without Customer instances.")
        created_count = 0
        for user in users_without_customer:
            Customer.objects.create(user=user, fullname=user.username or user.phone_number)
            created_count += 1
            self.stdout.write(f"Created Customer for User {user.id}")
        self.stdout.write(self.style.SUCCESS(f"Created {created_count} missing Customer instances."))
