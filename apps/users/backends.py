from django.contrib.auth.backends import ModelBackend
from apps.users.models import User

class CustomAuthenticationBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            if username:
                user = User.objects.get(username=username)
            elif 'phone_number' in kwargs:
                user = User.objects.get(phone_number=kwargs['phone_number'])
            else:
                return None
        except User.DoesNotExist:
            return None
        
        if user.check_password(password):
            return user
        return None
    