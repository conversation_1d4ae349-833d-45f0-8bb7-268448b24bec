from apps.users.models import User, <PERSON><PERSON>or, Customer, UserTypes, Courier
from django import forms
from django.contrib.auth.validators import UnicodeUsernameValidator
import re


def is_valid_to_add(phone_number):
    """
    Check if the given phone number is a valid Uzbekistan phone number.
    Returns True if valid, False otherwise.
    """
    pattern = r"^\+998\d{9}$"
    is_valid = bool(re.match(pattern, phone_number))
    if not is_valid:
        raise forms.ValidationError("Please enter a valid Uzbekistan phone number.")

    is_unique = User.objects.filter(phone_number=phone_number).exists()
    # if is_unique:
    # raise forms.ValidationError("User with this phone number already exists.")
    return True


class VendorAdminForm(forms.ModelForm):
    class Meta:
        model = Vendor
        fields = ["name", "inn", "logo", "is_verified", "region", "district", "work_start_time", "work_end_time", "total_bottles"]

    # Add fields from User model
    username_validator = UnicodeUsernameValidator()

    username = forms.Char<PERSON>ield(
        label="username",
        max_length=150,
        help_text="Required. 150 characters or fewer. Letters, digits and './+/-/_' (see below for file content) only.",
        validators=[username_validator],
        error_messages={
            "unique": "A user with that username already exists.",
        },
    )
    phone_number = forms.CharField(max_length=13, validators=[is_valid_to_add])
    password1 = forms.CharField(
        label="Password",
        widget=forms.PasswordInput,
        help_text="Your password must contain at least 8 characters.",
        min_length=8,
        error_messages={
            "min_length": "Your password must contain at least 8 characters."
        },
        required=False,
    )
    password2 = forms.CharField(
        label="Confirm password", widget=forms.PasswordInput, required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            if self.instance.user:
                self.initial["username"] = self.instance.user.username
                self.initial["phone_number"] = self.instance.user.phone_number
                self.initial["user_type"] = self.instance.user.user_type
                self.initial["password1"] = self.instance.user.password
        except:
            pass

    def save(self, commit=True):
        user_data = self.cleaned_data
        username = user_data.get("username")
        password = user_data.get("password1")
        passwword2 = user_data.get("password2")
        if password != passwword2:
            raise forms.ValidationError("Passwords do not match.")
        phone_number = user_data.get("phone_number")
        user_type = UserTypes.VENDOR
        try:
            try:
                user = User.objects.get(username=username)
            except:
                user = None
            if not user:
                user = User.objects.create(
                    username=username, phone_number=phone_number, user_type=user_type
                )
                user.set_password(password)
                user.save()
            else:
                user.username = username
                user.phone_number = phone_number
                user.user_type = user_type
                if password:
                    user.set_password(password)
                user.save()
            self.instance.user = user
        except Exception as e:
            print(e)
        return super().save(commit=commit)


class CustomerAdminForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = ["fullname", "avatar", "region", "district", "longitude", "latitude", "passport", "number2"]

    # Add fields from User model
    fullname = forms.CharField(max_length=200)
    phone_number = forms.CharField(max_length=13, validators=[is_valid_to_add])
    password1 = forms.CharField(
        label="Password",
        widget=forms.PasswordInput,
        help_text="Your password must contain at least 8 characters.",
        min_length=8,
        error_messages={
            "min_length": "Your password must contain at least 8 characters."
        },
        required=False,
    )
    password2 = forms.CharField(
        label="Confirm password", widget=forms.PasswordInput, required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            if self.instance.user:
                self.initial["username"] = self.instance.user.username
                self.initial["first_name"] = self.instance.user.first_name
                self.initial["avatar"] = self.instance.user.avatar
                self.initial["last_name"] = self.instance.user.last_name
                self.initial["phone_number"] = self.instance.user.phone_number
                self.initial["user_type"] = self.instance.user.user_type
                self.initial["password1"] = self.instance.user.password
        except:
            pass

    def save(self, commit=True):
        user_data = self.cleaned_data
        password = user_data.get("password1")
        phone_number = user_data.get("phone_number")
        user_type = UserTypes.CUSTOMER
        try:
            try:
                user = User.objects.get(phone_number=phone_number)
            except:
                user = None
            if not user:
                user = User.objects.create_user(
                    phone_number=phone_number,
                    user_type=user_type
                )
                user.set_password(password)
                user.save()
            else:
                user.user_type = user_type

                if password:
                    user.set_password(password)
                user.save()
            self.instance.user = user
        except Exception as e:
            print(e)
        return super().save(commit=commit)

    def clean_password2(self):
        password1 = self.cleaned_data.get("password1")
        password2 = self.cleaned_data.get("password2")
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError("Passwords don't match")
        return password2


class CourierAdminForm(forms.ModelForm):
    """
    The courier admin form
    """
    class Meta:
        model = Courier
        fields = ['user', 'vendors', 'fullname', 'address']

    phone_number = forms.CharField(max_length=13, validators=[is_valid_to_add])
    password1 = forms.CharField(
        label="Password",
        widget=forms.PasswordInput,
        help_text="Your password must contain at least 8 characters.",
        min_length=8,
        error_messages={
            "min_length": "Your password must contain at least 8 characters."
        },
        required=False,
    )
    password2 = forms.CharField(
        label="Confirm password", widget=forms.PasswordInput, required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        try:
            if self.instance.user:
                self.initial["phone_number"] = self.instance.user.phone_number
                self.initial["password1"] = self.instance.user.password
        except:
            pass

    def save(self, commit=True):
        user_data = self.cleaned_data
        password = user_data.get("password1")
        password2 = user_data.get("password2")
        phone_number = user_data.get("phone_number")
        user_type = UserTypes.COURIER
        if password != password2:
            raise forms.ValidationError("Passwords do not match.")
        try:
            try:
                user = User.objects.get(phone_number=phone_number)
            except:
                user = None
            if not user:
                user = User.objects.create_user(
                    phone_number=phone_number,
                    user_type=user_type
                )
                user.set_password(password)
                user.save()
            else:
                user.user_type = user_type
                if password:
                    user.set_password(password)
                user.save()
            self.instance.user = user
        except Exception as e:
            print(e)
        return super().save(commit=commit)
