from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.validators import ValidationError
from apps.users.common.validators import is_valid_uzb_phone_number as isValid
from apps.users.models import User, UserTypes


class MyTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        is_used_phone = isValid(username)
        if is_used_phone:
            user = User.objects.get(
                phone_number=username,
                user_type=UserTypes.CUSTOMER
            )
        else:
            user = User.objects.get(
                username=username,
                user_type=UserTypes.CUSTOMER
            )

        attrs["username"] = user.username
        if user:
            if user.check_password(password):
                data = super().validate(attrs)
                refresh = self.get_token(user)
                data["access"] = str(refresh.access_token)
                data["refresh"] = str(refresh)
                data["role"] = user.user_type
                return data
            else:
                raise ValidationError(
                    {"detail": "username/phone number or password is incorrect"}
                )
        else:
            raise ValidationError(
                {"detail": "username/phone number or password is incorrect"}
            )

    def get_token(self, user):
        token = super().get_token(user)
        token["username"] = user.username
        return token


class DashboardTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        username = attrs.get("username")
        password = attrs.get("password")

        is_used_phone = isValid(username)
        if is_used_phone:
            user = User.objects.get(phone_number=username)
        else:
            user = User.objects.get(username=username)

        # Check if user has appropriate role for dashboard access
        if user.user_type not in [UserTypes.VENDOR, UserTypes.ADMIN]:
            raise ValidationError(
                {"detail": "You do not have permission to access the dashboard"}
            )

        attrs["username"] = user.username
        if user:
            if user.check_password(password):
                data = super().validate(attrs)
                refresh = self.get_token(user)
                data["access"] = str(refresh.access_token)
                data["refresh"] = str(refresh)
                data["role"] = user.user_type
                return data
            else:
                raise ValidationError(
                    {"detail": "username/phone number or password is incorrect"}
                )
        else:
            raise ValidationError(
                {"detail": "username/phone number or password is incorrect"}
            )

    def get_token(self, user):
        token = super().get_token(user)
        token["username"] = user.username
        return token


class MyTokenObtainPairView(TokenObtainPairView):
    serializer_class = MyTokenObtainPairSerializer


class DashboardTokenObtainPairView(TokenObtainPairView):
    serializer_class = DashboardTokenObtainPairSerializer
