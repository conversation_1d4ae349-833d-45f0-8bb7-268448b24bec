"""
The courier urls
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from apps.users.couriers.views import CourierViewSet, TelegramAuthView


router = DefaultRouter()
router.register(r'couriers', CourierViewSet, basename='courier')

urlpatterns = [
    path('', include(router.urls)),
    path('telegram-auth/', TelegramAuthView.as_view(), name='telegram-auth'),
]
