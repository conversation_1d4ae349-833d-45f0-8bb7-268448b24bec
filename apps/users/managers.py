from django.contrib.auth.models import BaseUserManager
from django.db import models
from apps.users.common.utils import create_username


class CustomUserManager(BaseUserManager):
    def _create_user(self, username, phone_number, password, **extra_fields):
        if not username:
            username = create_username()
        username = self.model.normalize_username(username)

        if not phone_number:
            raise ValueError("The phone number must be set")

        from apps.users.models import User

        user = self.model(username=username, phone_number=phone_number, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def _create_superuser(self, username, phone_number, password, **extra_fields):
        if not username:
            raise ValueError("The given username must be set")
        username = self.model.normalize_username(username)
        user = self.model(username=username, phone_number=phone_number, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, phone_number, username=None, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(username, phone_number, password, **extra_fields)

    def create_superuser(self, username, phone_number, password, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("user_type", "ADMIN")

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self._create_superuser(username, phone_number, password, **extra_fields)


class VerifiedVendorManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(is_verified=True)

class CourierManager(BaseUserManager):
    """
    the courier manager
    """
    def create_courier(self, phone, name, password=None, **extra_fields):
        """
        Create and return a courier with an email, name, and password.
        """
        if not phone:
            raise ValueError("The Phone number must be set")

        # Check if phone number already exists in User model
        from apps.users.models import User

        phone = self.normalize_email(phone)
        courier = self.model(phone=phone, name=name, **extra_fields)
        courier.set_password(password)
        courier.save(using=self._db)
        return courier
