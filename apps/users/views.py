from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status

from apps.users.models import UserTypes
from apps.users.customers.serializers import CustomerSerializer, UserSerializer
from apps.users.couriers.serializers import CourierSerializer
from apps.users.vendors.serializers import VendorSerializer


class ProfileAPIView(APIView):
    """
    View to handle user profile.
    """
    permission_classes = [IsAuthenticated]

    # Clean mapping of user types to profile attributes and serializers
    PROFILE_MAP = {
        UserTypes.CUSTOMER: ('customer', CustomerSerializer),
        UserTypes.COURIER: ('courier', CourierSerializer),
        UserTypes.VENDOR: ('vendor', VendorSerializer),
        UserTypes.ADMIN: ('user', UserSerializer),
    }

    def get(self, request):
        """
        get user profile
        """
        user = request.user

        # Check if user type is supported
        if user.user_type not in self.PROFILE_MAP:
            return Response(
                {"detail": "User type not supported."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get profile attribute and serializer class
        profile_attr, serializer_class = self.PROFILE_MAP[user.user_type]
        if profile_attr == 'user':
            profile = user
        else:
            profile = getattr(user, profile_attr, None)

        # Check if profile exists
        if not profile:
            user_type_name = user.user_type.replace('_', ' ').title()
            return Response(
                {"detail": f"{user_type_name} profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Serialize and return profile data
        serializer = serializer_class(profile)
        return Response(serializer.data, status=status.HTTP_200_OK)
