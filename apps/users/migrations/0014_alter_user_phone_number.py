# Generated by Django 4.2.10 on 2025-06-13 17:19

import apps.users.common.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0013_alter_user_unique_together'),
    ]

    operations = [
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, error_messages={'unique': 'A user with that number already exists.'}, max_length=15, null=True, validators=[apps.users.common.validators.is_valid_uzb_phone_number]),
        ),
    ]
