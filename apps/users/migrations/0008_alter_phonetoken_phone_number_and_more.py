# Generated by Django 5.0.4 on 2025-05-11 06:34

import apps.users.common.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0007_remove_customer_address_customer_district_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="phonetoken",
            name="phone_number",
            field=models.CharField(
                max_length=20,
                validators=[apps.users.common.validators.is_valid_uzb_phone_number],
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="phone_number",
            field=models.CharField(
                blank=True,
                error_messages={"unique": "A user with that number already exists."},
                max_length=15,
                null=True,
                unique=True,
                validators=[apps.users.common.validators.is_valid_uzb_phone_number],
            ),
        ),
    ]
