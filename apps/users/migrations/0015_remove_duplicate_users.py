# Generated manually to remove duplicate users before applying unique constraint

from django.db import migrations
from django.db.models import Count


def remove_duplicate_users(apps, schema_editor):
    """
    Remove duplicate users with the same phone_number and user_type.
    Keep the oldest user (first created) and delete the rest.
    """
    User = apps.get_model('users', 'User')
    Customer = apps.get_model('users', 'Customer')
    
    # Find all phone numbers that have duplicates for the same user type
    duplicates = User.objects.values('phone_number', 'user_type').annotate(
        count=Count('id')
    ).filter(count__gt=1)
    
    for duplicate in duplicates:
        phone_number = duplicate['phone_number']
        user_type = duplicate['user_type']
        
        # Get all users with this phone number and user type, ordered by date_joined
        duplicate_users = User.objects.filter(
            phone_number=phone_number,
            user_type=user_type
        ).order_by('date_joined')
        
        # Keep the first (oldest) user and delete the rest
        users_to_delete = duplicate_users[1:]  # Skip the first one
        
        for user in users_to_delete:
            # Delete related customer profile if exists
            try:
                if hasattr(user, 'customer'):
                    user.customer.delete()
            except:
                pass
            
            # Delete the user
            user.delete()


def reverse_remove_duplicate_users(apps, schema_editor):
    """
    This migration cannot be reversed as we're deleting data.
    """
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0014_alter_user_phone_number'),
    ]

    operations = [
        migrations.RunPython(
            remove_duplicate_users,
            reverse_remove_duplicate_users,
        ),
    ]
