# Moved shared utilities to a common module
# This file will be refactored into the common module.

import uuid
from datetime import datetime, timedelta


def model_field_attr(model, model_field, attr):
    """
    Returns the specified attribute for the specified field on the model class.
    """
    fields = dict([(field.name, field) for field in model._meta.fields])
    return getattr(fields[model_field], attr)


def create_username():
    """
    Returns a UUID-based 'random' and unique username.
    This is required data for user models with a username field.
    """
    return str(uuid.uuid4())

def get_days_in_range(start_date, end_date):
    start_date = datetime.strptime(start_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d")
    
    # Barcha kunlarni olish
    delta = timedelta(days=1)
    days_list = []
    
    current_date = start_date
    while current_date <= end_date:
        days_list.append(current_date.date())
        current_date += delta 

    return days_list


