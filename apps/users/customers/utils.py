from apps.users.models import PhoneToken, User
import random
import string
from django.utils import timezone
from core.sms import eskiz


def generate_token(phone_number):
    phone_token = PhoneToken.objects.create(phone_number=phone_number)
    token = "".join(random.choices(string.digits, k=5))
    phone_token.token = token
    phone_token.expires_at = timezone.now() + timezone.timedelta(minutes=3)
    phone_token.save()
    # message = "Sizning maxsus kodiz: {}".format(token)
    # eskiz.send_sms(str(phone_number)[1:], message, from_whom="4546")
    return token


def verify_token(phone_number, token):
    try:
        phone_token = PhoneToken.objects.filter(phone_number=phone_number).last()
        if phone_token.token == token and phone_token.expires_at > timezone.now():
            phone_token.is_verified = True
            phone_token.save()
            phone_token.delete()
            user = User.objects.get(phone_number=phone_number)
            user.is_active = True
            user.save()
            return True
        return False
    except PhoneToken.DoesNotExist:
        return False
