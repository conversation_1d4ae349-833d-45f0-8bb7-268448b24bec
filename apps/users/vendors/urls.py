"""
Vendor urls
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from apps.users.vendors.views import RequestVendorViewSet, VendorViewSet, VendorAvailablePlacesViewSet


router = DefaultRouter()
router.register("request-to-vendor", RequestVendorViewSet, basename="sponsors")
router.register("vendors", VendorViewSet, basename="vendors")
router.register(
    "vendor-available-places",
    VendorAvailablePlacesViewSet,
    basename="vendor-available-places"
)

urlpatterns = [
    path("", include(router.urls)),
]
