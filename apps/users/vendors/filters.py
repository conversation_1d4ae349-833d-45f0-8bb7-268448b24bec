"""
Vendor and VendorClient filters
"""
from django_filters import rest_framework as filters

from apps.users.models import Vendor
from apps.users.vendors.models import VendorClient


class VendorFilter(filters.FilterSet):
    """
    Filter for Vendor model
    """
    created_at = filters.DateFromToRangeFilter(field_name="created_at")

    class Meta:
        """
        Meta class
        """
        model = Vendor
        fields = ("created_at",)


class VendorClientFilter(filters.FilterSet):
    """
    Filter for VendorClient model
    """
    created_at = filters.DateFromToRangeFilter(field_name="created_at")

    class Meta:
        """
        Meta class
        """
        model = VendorClient
        fields = ("created_at",)
