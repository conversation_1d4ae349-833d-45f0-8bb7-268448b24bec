"""
Vendor and VendorClient serializers
"""
from rest_framework import serializers
from apps.users.models import Vendor, UserTypes, User
from apps.users.vendors.models import RequestVendor, VendorClient, VendorAvailablePlaces, DamageReport
from apps.users.customers.serializers import UserSerializer, CustomerSerializer
from apps.helpers.serializers import RegionSerializer, DistrictSerializer


class VendorSerializer(serializers.ModelSerializer):
    """
    Vendor serializer
    """
    user = UserSerializer()
    region = RegionSerializer()
    district = DistrictSerializer()

    class Meta:
        """
        Meta class
        """
        model = Vendor
        fields = (
            "id",
            "user",
            "name",
            "logo",
            "region",
            "district",
            "work_start_time",
            "work_end_time",
            "created_at",
            "total_bottles",
            "description"
        )
        read_only_fields = ("created_at",)


class VendorCreateSerializer(serializers.ModelSerializer):
    """
    Vendor create serializer
    """
    phone_number = serializers.CharField(write_only=True, required=True)
    password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = Vendor
        fields = (
            "id",
            "phone_number",
            "password",
            "name",
            "inn",
            "logo",
            "region",
            "district",
            "work_start_time",
            "work_end_time",
            "description",
            "total_bottles",
        )

    def create(self, validated_data):
        """
        Create vendor
        """
        phone_number = validated_data.pop("phone_number")
        password = validated_data.pop("password")

        # Generate username automatically based on phone number
        username = f"user_{phone_number}"

        # Check if user with phone number already exists
        if User.objects.filter(phone_number=phone_number).exists():
            raise serializers.ValidationError("A user with this phone number already exists.")

        user = User.objects.create_user(
            phone_number=phone_number,
            username=username,
            password=password,
            user_type=UserTypes.VENDOR,
        )

        vendor = Vendor.objects.create(user=user, **validated_data)

        return vendor


class RequestVendorSerializer(serializers.ModelSerializer):
    """
    Request vendor serializer
    """
    class Meta:
        model = RequestVendor
        fields = (
            "id",
            "name",
            "phone_number",
            "description",
            "created_at"
        )


class VendorClientSerializer(serializers.ModelSerializer):
    """
    Vendor client serializer
    """
    vendor = VendorSerializer()
    customer = CustomerSerializer()
    region = RegionSerializer()
    district = DistrictSerializer()

    class Meta:
        """
        Meta class
        """
        model = VendorClient
        fields = ['id', 'vendor', 'customer', 'return_bottles',
                  'orders_count', 'additional_number', 'address', 'region', 'district']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        vendor_client = VendorClient.objects.get(id=data['id'])
        customer = data['customer']
        if not customer:
            data['customer'] = {}
            data['customer']['number1'] = vendor_client.phone_number
            data['customer']['fullname'] = vendor_client.fullname
            data['customer']['passport'] = vendor_client.passport

        return data


class VendorClientCreateSerializer(serializers.ModelSerializer):
    """
    Vendor client create serializer
    """
    class Meta:
        """
        Meta class
        """
        model = VendorClient
        fields = [
            'id',
            'phone_number',
            'fullname',
            'passport',
            'return_bottles',
            'additional_number',
            'address',
            'region',
            'district'
        ]

    def create(self, validated_data):
        """
        Create vendor client and update vendor stats if return_bottles > 0
        """
        request_user = self.context.get('request').user
        if request_user.user_type == UserTypes.VENDOR:
            vendor = request_user.vendor
            return_bottles = validated_data.get('return_bottles', 0)

            vendor_client = VendorClient.objects.create(
                vendor=vendor,
                customer=None,
                **validated_data
            )

            # Update vendor stats if old client has bottles
            if return_bottles > 0:
                self._update_vendor_stats_for_old_client(vendor, return_bottles)

            return vendor_client

    def _update_vendor_stats_for_old_client(self, vendor, return_bottles):
        """
        Update vendor stats when creating a client with existing bottles.
        """
        from apps.users.vendors.models import VendorStats
        vendor_stats, created = VendorStats.objects.get_or_create(
            vendor=vendor,
            defaults={'free_bottles': 0, 'busy_bottles': 0}
        )

        # Update vendor stats: old client has bottles
        # free_bottles decrease (bottles are not free, they're with the client)
        # busy_bottles increase (bottles are busy with the client)
        vendor_stats.free_bottles = max(0, vendor_stats.free_bottles - return_bottles)
        vendor_stats.busy_bottles += return_bottles
        vendor_stats.save()


class VendorAvailablePlacesSerializer(serializers.ModelSerializer):
    """
    Vendor available places serializer
    """
    class Meta:
        model = VendorAvailablePlaces
        fields = '__all__'

    def validate(self, data):
        """
        Validate vendor available places
        """
        vendor = data.get('vendor')
        region = data.get('region')
        districts = set([d.id for d in data.get('districts', [])])

        existing = VendorAvailablePlaces.objects.filter(vendor=vendor, region=region)
        # Exclude current instance if updating
        instance = getattr(self, 'instance', None)
        if instance and instance.pk:
            existing = existing.exclude(pk=instance.pk)

        for place in existing:
            existing_districts = set(place.districts.values_list('id', flat=True))
            if districts == existing_districts:
                raise serializers.ValidationError(
                    "This combination of vendor, region, and districts already exists."
                )
        return data


class DamageReportSerializer(serializers.ModelSerializer):
    """
    Serializer for the DamageReport model
    """
    vendor_id = serializers.IntegerField(write_only=True)
    user_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = DamageReport
        fields = ["vendor_id", "user_id", "damaged_bottles", "is_covered", "datetime"]

    def validate(self, attrs):
        attrs["vendor"] = Vendor.objects.get(id=attrs.pop("vendor_id"))
        vendor_client = VendorClient.objects.get(customer_id=attrs.pop("user_id"), vendor=attrs["vendor"])
        attrs["user"] = vendor_client.customer

        # Check if damaged_bottles exceeds return_bottles
        damaged_bottles = attrs.get("damaged_bottles")
        if damaged_bottles > vendor_client.return_bottles:
            raise serializers.ValidationError({
                "damaged_bottles": "Damaged bottles cannot exceed the customer's returned bottles."
            })

        attrs["vendor_client"] = vendor_client
        return attrs

    def create(self, validated_data):
        vendor_client = validated_data.pop("vendor_client")
        damaged_bottles = validated_data["damaged_bottles"]

        # Deduct damaged bottles from return_bottles
        vendor_client.return_bottles -= damaged_bottles
        vendor_client.save()

        return super().create(validated_data)
