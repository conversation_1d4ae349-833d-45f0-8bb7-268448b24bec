"""
API For Vendors
"""
from datetime import datetime, timedelta
from collections import defaultdict

from django.db.models import Q, Func, Value
from .utils import normalize_phone_number
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend

from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import SearchFilter

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from apps.helpers.models import Region
from apps.helpers.serializers import RegionSerializer, DistrictSerializer
from apps.users.models import Vendor, UserTypes
from apps.users.vendors.models import RequestVendor, VendorAvailablePlaces, VendorClient
from apps.users.vendors.filters import VendorFilter
from apps.users.vendors.serializers import (
    RequestVendorSerializer, VendorSerializer, VendorClientSerializer, 
    VendorClientCreateSerializer, VendorCreateSerializer, VendorAvailablePlacesSerializer,
    DamageReportSerializer
)
from apps.orders.models import Order, OrderStatus


class RequestVendorViewSet(viewsets.ModelViewSet):
    """
    Viewset For Request Vendor
    """
    queryset = RequestVendor.objects.all()
    serializer_class = RequestVendorSerializer
    http_method_names = ["post", "get"]

    def get_queryset(self):
        user = self.request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.ADMIN:
                return self.queryset
        return Response(status=status.HTTP_403_FORBIDDEN)


class VendorViewSet(viewsets.ModelViewSet):
    """
    Viewset For Vendor
    """
    queryset = Vendor.objects.all()
    serializer_class = VendorSerializer
    http_method_names = ["get", "post", "put", "patch"]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_class = VendorFilter
    search_fields = ["name", "user__phone_number", "inn", "id"]

    @action(detail=False, methods=["get"])
    def me(self, request):
        """
        Get Vendor Info
        """
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.VENDOR:
                vendor_obj = user.vendor
                serializer = VendorSerializer(vendor_obj)
                data = serializer.data
                data["inn"] = vendor_obj.inn
                return Response(data, status=status.HTTP_200_OK)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_401_UNAUTHORIZED)

    def update(self, request, *args, **kwargs):
        user = request.user
        if user.user_type != UserTypes.ADMIN:
            return Response(
                {"detail": "You do not have permission to update vendors."},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        user = request.user
        if user.user_type != UserTypes.ADMIN:
            return Response(
                {"detail": "You do not have permission to update vendors."},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'region_id',
                openapi.IN_QUERY,
                description='ID of the region to filter by',
                type=openapi.TYPE_INTEGER,
                required=False
            )
        ]
    )
    @action(detail=False, methods=["get"])
    def count(self, request):
        """
        Returns the total number of vendors
        """
        count = Vendor.objects.count()
        return Response(
            {"vendor_count": count}
        )

    @action(detail=True, methods=["get"])
    def get_available_regions(self, request, pk=None):
        """
        Get Available Regions
        """
        user = request.user
        if not user.is_authenticated:
            return Response(status=status.HTTP_401_UNAUTHORIZED)

        vendor = Vendor.objects.get(id=pk)
        region_id = request.query_params.get("region_id")
        if region_id:
            region = VendorAvailablePlaces.objects.filter(vendor=vendor, region_id=region_id).last()
            if not region:
                return Response({"message": "Region not found"}, status=status.HTTP_404_NOT_FOUND)
            districts = region.districts.all()
            district_serializer = DistrictSerializer(districts, many=True)
            return Response(district_serializer.data, status=status.HTTP_200_OK)

        regions = Region.objects.filter(vendoravailableplaces__vendor=vendor)
        serializer = RegionSerializer(regions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=["get"])
    def is_region_available(self, request):
        """
        Check if a region is available for a vendor
        """
        vendor_id = request.query_params.get("vendor_id")
        region_id = request.query_params.get("region_id")

        if not vendor_id or not region_id:
            return Response(
                {"detail": "vendor_id va region_id talab qilinadi"},
                status=status.HTTP_400_BAD_REQUEST)

        try:
            vendor = Vendor.objects.get(id=vendor_id)
        except Vendor.DoesNotExist:
            return Response({"detail": "Vendor topilmadi"}, status=status.HTTP_404_NOT_FOUND)

        exists = VendorAvailablePlaces.objects.filter(vendor=vendor, region_id=region_id).exists()

        if exists:
            return Response({"available": True}, status=status.HTTP_200_OK)

        return Response(
            {"available": False, "message": "Ushbu hududga xizmat ko'rsatilmaydi"},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=["get"])
    def is_district_available(self, request):
        """
        Check if a district is available for a vendor
        """
        vendor_id = request.query_params.get("vendor_id")
        region_id = request.query_params.get("region_id")
        district_id = request.query_params.get("district_id")

        if not vendor_id or not region_id or not district_id:
            return Response(
                {"detail": "vendor_id, region_id, va district_id talab qilinadi"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            vendor = Vendor.objects.get(id=vendor_id)
        except Vendor.DoesNotExist:
            return Response({"detail": "Vendor topilmadi"}, status=status.HTTP_404_NOT_FOUND)

        # Vendorning regioni va districtlarini tekshirish
        available_region = VendorAvailablePlaces.objects.filter(vendor=vendor, region_id=region_id).first()

        if not available_region:
            return Response({"message": "Ushbu regionga xizmat ko'rsatilmaydi"}, status=status.HTTP_400_BAD_REQUEST)

        district = available_region.districts.filter(id=district_id).first()

        if district:
            return Response({"available": True}, status=status.HTTP_200_OK)

        return Response(
            {"available": False, "message": "Ushbu hududga xizmat ko'rsatilmaydi"},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'])
    def clients(self, request, *args, **kwargs):
        """
        Get clients for a vendor
        """
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.VENDOR:
                vendor = user.vendor
                search = request.query_params.get("search")
                if search:
                    # Build the query conditions
                    query_conditions = Q()

                    # Search in fullname (case-insensitive)
                    query_conditions |= Q(customer__fullname__icontains=search)

                    # If search looks like a phone number (starts with + or contains only digits)
                    if search.startswith('+') or search.replace(' ', '').replace('-', '').replace('(', '').replace(')', '').isdigit():
                        # Normalize the search term
                        normalized_search = normalize_phone_number(search)

                        # Search in various phone number fields
                        phone_fields = [
                            'customer__user__phone_number',
                            'customer__number2',
                            'additional_number'
                        ]

                        for field in phone_fields:
                            # Search with both original and normalized versions
                            query_conditions |= Q(**{f"{field}__icontains": search})

                            # Create a condition for normalized search using startswith
                            if normalized_search:
                                normalized_field = f"normalized_{field.replace('__', '_').replace('customer_', '')}"
                                query_conditions |= Q(**{f"{normalized_field}__startswith": normalized_search})

                    # Apply annotations and filter
                    clients = VendorClient.objects.annotate(
                        normalized_user_phone_number=Func(
                            'customer__user__phone_number',
                            Value('[^0-9]'),
                            Value(''),
                            function='regexp_replace'
                        ),
                        normalized_number2=Func(
                            'customer__number2',
                            Value('[^0-9]'),
                            Value(''),
                            function='regexp_replace'
                        ),
                        normalized_additional_number=Func(
                            'additional_number',
                            Value('[^0-9]'),
                            Value(''),
                            function='regexp_replace'
                        )
                    ).filter(
                        vendor=vendor
                    ).filter(
                        query_conditions
                    ).distinct().order_by('-updated_at')
                else:
                    clients = VendorClient.objects.filter(vendor=vendor).order_by('-updated_at')

                serializer = VendorClientSerializer(clients, many=True)
                pagination = self.paginate_queryset(serializer.data)
                if pagination:
                    return self.get_paginated_response(pagination)
                return Response(serializer.data)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_403_FORBIDDEN)

    def _get_vendor_for_stats(self, request, pk=None):
        """
        Helper method to determine vendor for stats endpoints
        """
        user = request.user
        if not user.is_authenticated:
            return None, Response(status=status.HTTP_401_UNAUTHORIZED)

        # Determine the vendor based on user type and request parameters
        if pk is None:
            # No vendor_id provided - use the requesting user's vendor
            if user.user_type == UserTypes.VENDOR:
                vendor = user.vendor
            else:
                return None, Response(
                    {"detail": "Vendor ID is required for non-vendor users"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # Vendor ID provided - check permissions
            if user.user_type == UserTypes.ADMIN:
                try:
                    vendor = Vendor.objects.get(id=pk)
                except Vendor.DoesNotExist:
                    return None, Response({"detail": "Vendor not found"}, status=status.HTTP_404_NOT_FOUND)
            else:
                return None, Response(
                    {"detail": "You do not have permission to view other vendors' stats"},
                    status=status.HTTP_403_FORBIDDEN
                )

        return vendor, None

    @action(detail=True, methods=["get"], url_path='stats')
    def get_vendor_stats(self, request, pk=None):
        """
        Get vendor statistics for a specific vendor
        For admins: access any vendor's stats via /api/v1/users/vendors/vendors/{vendor_id}/stats/
        """
        return self._get_stats(request, pk)

    @action(detail=False, methods=["get"], url_path='stats')
    def get_my_stats(self, request):
        """
        Get vendor statistics for the currently logged-in vendor
        For vendors: access their own stats via /api/v1/users/vendors/vendors/stats/
        """
        return self._get_stats(request, None)

    def _get_stats(self, request, pk=None):
        """
        Helper method to get vendor stats (bottle stats implementation)
        """
        vendor, error_response = self._get_vendor_for_stats(request, pk)
        if error_response:
            return error_response

        # Get date range from query parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if not start_date or not end_date:
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=7)
        else:
            try:
                start_date = datetime.strptime(str(start_date), '%Y-%m-%d').date()
                end_date = datetime.strptime(str(end_date), '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    "detail": "Invalid date format. Please use YYYY-MM-DD."
                }, status=status.HTTP_400_BAD_REQUEST)

        # Calculate customer count
        customers = VendorClient.objects.filter(vendor=vendor).count()

        # Calculate turnover for the date range
        orders = Order.objects.filter(
            vendor=vendor,
            status=OrderStatus.FINISHED,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )
        turnover = sum(order.quantity * order.product.price for order in orders)

        # Get vendor stats
        from apps.users.vendors.models import VendorStats
        vendor_stats, created = VendorStats.objects.get_or_create(vendor=vendor)

        # Initialize free_bottles and busy_bottles if newly created
        if created:
            vendor_stats.free_bottles = vendor.total_bottles
            vendor_stats.busy_bottles = 0

        vendor_stats.save()

        return Response({
            "vendor_id": vendor.id,
            "total_bottles": vendor.total_bottles,
            "busy_bottles": vendor_stats.busy_bottles,
            "free_bottles": vendor_stats.free_bottles,
            "customers": customers,
            "turnover": turnover,
            "date_range": {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d')
            }
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=["get"], url_path='total_order_stats')
    def vendor_total_order_stats(self, request, pk=None):
        """
        Returns total order count and total order amount for a specific vendor
        For admins: access any vendor's stats via /api/v1/users/vendors/vendors/{vendor_id}/total_order_stats/
        """
        return self._get_total_order_stats(request, pk)

    @action(detail=False, methods=["get"], url_path='total_order_stats')
    def my_total_order_stats(self, request):
        """
        Returns total order count and total order amount for the currently logged-in vendor
        For vendors: access their own stats via /api/v1/users/vendors/vendors/total_order_stats/
        """
        return self._get_total_order_stats(request, None)

    def _get_total_order_stats(self, request, pk=None):
        """
        Helper method to get total order stats for a vendor
        """
        vendor, error_response = self._get_vendor_for_stats(request, pk)
        if error_response:
            return error_response

        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        if not start_date or not end_date:
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=7)

        try:
            start_date = datetime.strptime(str(start_date), '%Y-%m-%d').date()
            end_date = datetime.strptime(str(end_date), '%Y-%m-%d').date()
        except ValueError:
            return Response({
                "detail": "Invalid date format. Please use YYYY-MM-DD."
            }, status=status.HTTP_400_BAD_REQUEST)

        orders = Order.objects.filter(
            vendor=vendor,
            status=OrderStatus.FINISHED,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )

        # Calculate sales per day
        sales_count_per_day = defaultdict(int)
        total_sales_per_day = defaultdict(float)

        for order in orders:
            order_date = order.created_at.date()
            sales_count_per_day[order_date] += order.quantity
            total_sales_per_day[order_date] += order.quantity * order.product.price

        # Format daily stats
        stats = {
            "sales_count_per_day":
                {str(date): sales_count_per_day[date] for date in sorted(sales_count_per_day.keys())},
            "total_sales_per_day":
                {str(date): total_sales_per_day[date] for date in sorted(total_sales_per_day.keys())}
        }

        return Response({
            "vendor_id": vendor.id,
            **stats
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"], url_path="report-damage")
    def report_damage(self, request):
        """
        API endpoint to report damaged bottles
        """
        # Restrict access to vendors only
        if request.user.user_type != UserTypes.VENDOR:
            return Response(
                {"detail": "You do not have permission to access this endpoint."},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = DamageReportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Save the damage report
        serializer.save()

        # Adjust total_bottles directly
        vendor = serializer.validated_data['vendor']
        damaged_bottles = serializer.validated_data['damaged_bottles']
        vendor.total_bottles -= damaged_bottles
        vendor.save()

        return Response({"detail": "Damage reported successfully."}, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"], url_path="add-bottles")
    def add_bottles(self, request):
        """
        API endpoint to add new bottles to vendor's stock.
        This endpoint accumulates bottles rather than replacing the total.
        """
        # Restrict access to vendors only
        if request.user.user_type != UserTypes.VENDOR:
            return Response(
                {"detail": "You do not have permission to access this endpoint."},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate input
        try:
            new_bottles = int(request.data.get('new_bottles', 0))
            if new_bottles <= 0:
                return Response(
                    {"detail": "Number of new bottles must be positive."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (TypeError, ValueError):
            return Response(
                {"detail": "Invalid number of bottles."},
                status=status.HTTP_400_BAD_REQUEST
            )

        vendor = request.user.vendor

        # Get vendor stats
        from apps.users.vendors.models import VendorStats
        vendor_stats, created = VendorStats.objects.get_or_create(vendor=vendor)

        # Update bottles count
        previous_total = vendor.total_bottles
        vendor.total_bottles += new_bottles
        vendor.save()

        # Update vendor stats - add new bottles to free_bottles
        vendor_stats.free_bottles += new_bottles
        # busy_bottles stays the same - no bottles moved to/from clients
        vendor_stats.save()

        return Response({
            "detail": f"Successfully added {new_bottles} bottles.",
            "previous_total": previous_total,
            "new_total": vendor.total_bottles,
            "free_bottles": vendor_stats.free_bottles,
            "busy_bottles": vendor_stats.busy_bottles
        }, status=status.HTTP_200_OK)

    @action(detail=False, methods=["post"])
    def create_client(self, request, *args, **kwargs):
        """
        Create a new client for a vendor
        """
        user = request.user
        if user.is_authenticated:
            if user.user_type == UserTypes.VENDOR:
                serializer = VendorClientCreateSerializer(data=request.data, context={'request': request})
                if serializer.is_valid():
                    serializer.save()
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            return Response(status=status.HTTP_403_FORBIDDEN)
        return Response(status=status.HTTP_403_FORBIDDEN)

    def get_serializer_class(self):
        if self.action == "create_client":
            return VendorClientCreateSerializer
        if self.action == "create":
            return VendorCreateSerializer
        return super().get_serializer_class()


class VendorAvailablePlacesViewSet(viewsets.ModelViewSet):
    """
    Viewset For Vendor Available Places
    """
    queryset = VendorAvailablePlaces.objects.all()
    serializer_class = VendorAvailablePlacesSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        vendor_id = self.request.query_params.get("vendor_id")
        user = self.request.user
        if vendor_id and user.is_authenticated and user.user_type == UserTypes.ADMIN:
            queryset = queryset.filter(vendor_id=vendor_id)
        return queryset

    @action(detail=True, methods=['delete'], url_path='districts')
    def delete_districts(self, request, pk=None):
        """
        Custom action to delete districts from a VendorAvailablePlaces instance.
        Expects a JSON body with a list of district IDs to remove:
        {
            "district_ids": [1, 2, 3]
        }.
        """
        instance = self.get_object()
        district_ids = request.data.get('district_ids', [])

        if not isinstance(district_ids, list) or not all(isinstance(i, int) for i in district_ids):
            return Response({"detail": "district_ids must be a list of integers."},
                            status=status.HTTP_400_BAD_REQUEST)

        # Get the current districts and remove the specified ones
        current_districts = list(instance.districts.all())
        districts_to_keep = [
            district for district in current_districts if district.id not in district_ids]

        # Update the districts field with the filtered districts
        instance.districts.set(districts_to_keep)

        serializer = self.get_serializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)
