# Generated by Django 5.0.4 on 2025-05-14 18:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0009_alter_vendor_total_bottles"),
        ("vendors", "0004_rename_order_count_vendorclient_orders_count"),
    ]

    operations = [
        migrations.CreateModel(
            name="VendorStats",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("total_bottles", models.IntegerField(default=0)),
                ("ordered_bottles", models.IntegerField(default=0)),
                ("available_bottles", models.IntegerField(default=0)),
                ("customer_count", models.IntegerField(default=0)),
                (
                    "weekly_turnover",
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    "vendor",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stats",
                        to="users.vendor",
                    ),
                ),
            ],
            options={
                "verbose_name": "Vendor Statistics",
                "verbose_name_plural": "Vendor Statistics",
                "db_table": "vendor_stats",
            },
        ),
    ]
