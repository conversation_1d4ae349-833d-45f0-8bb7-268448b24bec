# Generated by Django 5.0.4 on 2025-05-12 06:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0009_alter_vendor_total_bottles"),
        ("vendors", "0004_rename_order_count_vendorclient_orders_count"),
    ]

    operations = [
        migrations.CreateModel(
            name="DamageReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("damaged_bottles", models.IntegerField()),
                ("is_covered", models.BooleanField()),
                ("datetime", models.DateTimeField()),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="damage_reports",
                        to="users.customer",
                    ),
                ),
                (
                    "vendor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="damage_reports",
                        to="users.vendor",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
