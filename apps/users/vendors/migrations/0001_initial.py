# Generated by Django 4.0.6 on 2025-04-17 09:32

import apps.users.validators
from django.db import migrations, models
import django.db.models.deletion
import smart_selects.db_fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('helpers', '0001_initial'),
        ('users', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='RequestVendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('phone_number', models.CharField(max_length=255, validators=[apps.users.validators.is_valid_uzb_phone_number])),
                ('description', models.TextField()),
            ],
            options={
                'verbose_name': 'Requested vendor',
                'verbose_name_plural': 'Requested vendors',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='VendorClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('phone_number', models.CharField(blank=True, max_length=255, null=True, validators=[apps.users.validators.is_valid_uzb_phone_number])),
                ('fullname', models.CharField(blank=True, max_length=100, null=True)),
                ('passport', models.CharField(blank=True, max_length=12, null=True)),
                ('available_bottles', models.IntegerField(default=0)),
                ('additional_number', models.CharField(blank=True, max_length=255, null=True, validators=[apps.users.validators.is_valid_uzb_phone_number])),
                ('address', models.CharField(max_length=255)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendors', to='users.customer')),
                ('district', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='helpers.district')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='helpers.region')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='clients', to='users.vendor')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='VendorAvailablePlaces',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('districts', smart_selects.db_fields.ChainedManyToManyField(auto_choose=True, chained_field='region', chained_model_field='region', to='helpers.district')),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='helpers.region')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='places_available', to='users.vendor')),
            ],
        ),
    ]
