# Generated by Django 5.2 on 2025-04-09 18:09

import apps.products.validators
import apps.users.validators
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('fullname', models.CharField(blank=True, max_length=100, null=True)),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('status', models.CharField(choices=[('to_order', 'To Order'), ('order_accepted', 'Order Accepted'), ('order_rejected', 'Order Rejected'), ('delivering', 'Delivering'), ('done', 'Done')], default='to_order', max_length=100)),
                ('returned_bottles', models.IntegerField(blank=True, default=0, null=True)),
                ('total_returned_bottles', models.IntegerField(blank=True, default=0, null=True)),
                ('address', models.CharField(max_length=255)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, validators=[apps.users.validators.is_valid_uzb_phone_number])),
                ('additional_number', models.CharField(blank=True, max_length=15, null=True, validators=[apps.users.validators.is_valid_uzb_phone_number])),
                ('passport', models.CharField(blank=True, max_length=128, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('deliverable_time', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'order',
                'verbose_name_plural': 'Orders',
            },
        ),
        migrations.CreateModel(
            name='OrderApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('new', 'New'), ('accepted', 'Accepted'), ('rejected', 'Rejected')], default='new', max_length=100)),
            ],
            options={
                'verbose_name': 'application',
                'verbose_name_plural': 'Applications',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='OrderComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('text', models.TextField()),
                ('rating', models.FloatField(validators=[apps.products.validators.is_valid_rating])),
            ],
            options={
                'verbose_name': 'order comment',
                'verbose_name_plural': 'Order comments',
                'ordering': ('-created_at',),
            },
        ),
    ]
