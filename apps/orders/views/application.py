"""
order application viewset
"""
from rest_framework import viewsets
from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
from django.core.exceptions import ObjectDoesNotExist
from rest_framework.response import Response
from rest_framework import status

from apps.users.models import Customer, UserTypes
from apps.orders.models.order import OrderStatus
from apps.orders.models.application import OrderApplication, ApplicationStatus
from apps.orders.serializers import OrderApplicationSerializer


class OrderApplicationViewSet(viewsets.ModelViewSet):
    """
    order application view
    """
    queryset = OrderApplication.objects.all()
    serializer_class = OrderApplicationSerializer
    permission_classes = [permissions.IsAuthenticated]
    http_method_names = ["get", "put"]

    def get_queryset(self):
        user = self.request.user
        try:
            if user.is_authenticated:
                # Get new applications only (first-time orders)
                base_queryset = self.queryset.filter(status=ApplicationStatus.NEW)

                if user.user_type == UserTypes.VENDOR:
                    vendor_user = user.vendor
                    return base_queryset.filter(order__vendor=vendor_user).exclude(
                        order__status=OrderStatus.CANCELLED)
                if user.user_type == UserTypes.CUSTOMER:
                    try:
                        customer_user = user.customer
                    except Customer.DoesNotExist:
                        return Response(
                            {
                                "status": "error",
                                "message": "Your account is not properly set up as a customer. Please contact support."
                            },
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    return base_queryset.filter(order__customer=customer_user).exclude(
                        order__status=OrderStatus.CANCELLED)
                if user.user_type == UserTypes.ADMIN:
                    return base_queryset.exclude(order__status=OrderStatus.CANCELLED)
                if user.user_type == UserTypes.COURIER:
                    # Only show applications from courier's vendors
                    if hasattr(user.courier, 'vendors') and user.courier.vendors.exists():
                        return base_queryset.filter(order__vendor__in=user.courier.vendors.all()).exclude(
                            order__status=OrderStatus.CANCELLED)
                    return Response(
                        {
                            "status": "error",
                            "message": "You are not assigned to any vendor"
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )
            raise PermissionDenied("You don't have permission to access")
        except ObjectDoesNotExist as ode:
            return Response(
                {
                    "status": "error",
                    "message": "The requested resource was not found",
                    "details": str(ode)
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except PermissionDenied as pd:
            return Response(
                {
                    "status": "error",
                    "message": "You don't have permission to access this resource",
                    "details": str(pd)
                },
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        """
        Update an application with improved error handling
        """
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            
            if serializer.is_valid():
                result = serializer.save()
                return Response(
                    serializer.data,
                    status=status.HTTP_200_OK
                )
            
            return Response(
                {
                    "status": "error",
                    "message": "Invalid data provided",
                    "details": serializer.errors
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except ObjectDoesNotExist:
            return Response(
                {
                    "status": "error",
                    "message": "The requested application was not found"
                },
                status=status.HTTP_404_NOT_FOUND
            )
        except PermissionDenied:
            return Response(
                {
                    "status": "error",
                    "message": "You don't have permission to update this application"
                },
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while updating the application",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
