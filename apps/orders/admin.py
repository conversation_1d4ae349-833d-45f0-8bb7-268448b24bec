"""
the order admin configuration
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.orders.models import Order, OrderApplication


@admin.register(Order)
class OrderAdmin(ModelAdmin):
    """
    order admin configuration
    """
    list_display = ('id', 'product', 'vendor', 'status', 'region', 'delivered_at')
    list_filter = ('status', 'region')
    search_fields = (
        'product__title', 'district__name', 'additional_number', 'delivered_at', 'passport')
    date_hierarchy = 'delivered_at'
    readonly_preprocess_fields = {}


@admin.register(OrderApplication)
class OrderApplicationAdmin(ModelAdmin):
    """
    the order application admin
    """
    list_display = ('id', 'order', 'status')
    list_filter = ('status',)
    readonly_preprocess_fields = {}
