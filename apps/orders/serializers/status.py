"""
status order serializer
"""

from rest_framework import serializers

from apps.users.models import UserTypes
from apps.orders.models import OrderStatus


class OrderStatusChangeSerializer(serializers.Serializer):
    """
    Serializer for changing order status.
    """
    status = serializers.ChoiceField(choices=OrderStatus.choices)
    courier_id = serializers.IntegerField(required=False, allow_null=True)
    delivered_quantity = serializers.IntegerField(required=False, allow_null=True)
    returned_bottles = serializers.IntegerField(required=False, allow_null=True)

    def validate(self, attrs):
        """
        Validate the status change based on user permissions and current status.
        """
        user = self.context['request'].user
        current_order = self.context.get('order')
        new_status = attrs['status']
        current_status = current_order.status

        # Validate user permissions first - fail fast approach
        self._validate_user_permissions(user, current_order)

        # Get allowed transitions based on user type
        allowed_transitions = self._get_allowed_transitions(user, current_order)

        # Check if the transition is allowed
        if current_status not in allowed_transitions or new_status not in allowed_transitions.get(
                current_status, []):
            raise serializers.ValidationError({
                "message": f"Cannot change order status from {current_status} to {new_status}"
            })

        # Validate courier_id presence when status is delivering
        if new_status == 'delivering':
            courier_id = attrs.get('courier_id')
            if not courier_id:
                raise serializers.ValidationError({
                    "courier_id": "This field is required when status is 'delivering'."
                })

        return attrs

    def _validate_user_permissions(self, user, order):
        """
        Validate user has permission to modify this order
        """
        if user.user_type == UserTypes.VENDOR and order.vendor != user.vendor:
            raise serializers.ValidationError({"message": "You can only manage your own orders"})

        if user.user_type == UserTypes.CUSTOMER and order.customer != user.customer:
            raise serializers.ValidationError({"message": "You can only manage your own orders"})

        if user.user_type == UserTypes.COURIER:
            # First check if courier belongs to the right vendor
            if hasattr(user.courier, 'vendors') and user.courier.vendors.exists() \
                    and order.vendor not in user.courier.vendors.all():
                raise serializers.ValidationError({"message": "You can only manage orders from your vendors"})
            
            # Then check if courier is assigned to this order or if it's unassigned
            if order.courier is not None and order.courier != user.courier:
                raise serializers.ValidationError({"message": "This order is assigned to another courier"})
            elif order.courier is None and order.status not in [
                    OrderStatus.PENDING, OrderStatus.ACCEPTED]:
                raise serializers.ValidationError({"message": "You can only pick up new or accepted orders"})

        if user.user_type not in [UserTypes.VENDOR, UserTypes.CUSTOMER, UserTypes.COURIER, UserTypes.ADMIN]:
            raise serializers.ValidationError({"message": "You don't have permission to change order status"})

    def _get_allowed_transitions(self, user, order):
        """
        Get allowed status transitions based on user type
        """
        if user.user_type in [UserTypes.VENDOR, UserTypes.ADMIN, UserTypes.COURIER]:
            return {
                OrderStatus.PENDING: [
                    OrderStatus.ACCEPTED, OrderStatus.REJECTED
                ],
                OrderStatus.ACCEPTED: [
                    OrderStatus.DELIVERING, OrderStatus.REJECTED
                ],
                OrderStatus.DELIVERING: [
                    OrderStatus.FINISHED, OrderStatus.REJECTED
                ],
            }

        if user.user_type == UserTypes.CUSTOMER:
            return {
                OrderStatus.PENDING: [
                    OrderStatus.REJECTED, OrderStatus.CANCELLED
                ],
                OrderStatus.ACCEPTED: [OrderStatus.CANCELLED],
                OrderStatus.DELIVERING: [OrderStatus.CANCELLED],
            }

        if user.user_type == UserTypes.COURIER:
            # If order is unassigned, only allow accepting/rejecting new or accepted orders
            if order.courier is None:
                return {
                    OrderStatus.PENDING: [
                        OrderStatus.ACCEPTED,
                        OrderStatus.REJECTED
                    ],
                    OrderStatus.ACCEPTED: [
                        OrderStatus.DELIVERING,  # This will assign the courier
                        OrderStatus.REJECTED
                    ]
                }
            # If courier is assigned to this order, allow full status changes
            elif order.courier == user.courier:
                return {
                    OrderStatus.ACCEPTED: [
                        OrderStatus.DELIVERING,
                        OrderStatus.REJECTED
                    ],
                    OrderStatus.DELIVERING: [
                        OrderStatus.FINISHED,
                        OrderStatus.REJECTED
                    ]
                }
            # If different courier is assigned, allow no transitions
            return {}

        return {}
