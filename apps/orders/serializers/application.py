"""
order application serializers
"""
from rest_framework import serializers

from apps.orders.models.order import OrderStatus
from apps.orders.models.application import OrderApplication, ApplicationStatus
from apps.orders.serializers.order import OrderSerializer
from apps.users.models import UserTypes


class OrderApplicationSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderApplication model.
    """
    order = OrderSerializer(read_only=True)

    class Meta:
        """
        the meta class
        """
        model = OrderApplication
        fields = ("id", "order", "status", "created_at", "updated_at")
        read_only_fields = ("id", "order", "created_at", "updated_at")

    def update(self, instance, validated_data):
        """
        Update an order application.
        """
        from apps.users.vendors.models import VendorClient

        user = self.context["request"].user
        order = instance.order

        # Allow both vendors and couriers to update applications
        if user.user_type == UserTypes.VENDOR:
            if order.vendor != user.vendor:
                raise serializers.ValidationError(
                    {"message": "You can only update applications from your vendor"}
                )
        elif user.user_type == UserTypes.COURIER:
            if not (hasattr(user.courier, 'vendor') and user.courier.vendor and order.vendor == user.courier.vendor):
                raise serializers.ValidationError(
                    {"message": "You can only update applications from your vendor"}
                )
        else:
            raise serializers.ValidationError(
                {"message": "You do not have permission to update this application"}
            )

        status = validated_data.get("status")
        if status:
            instance.status = status
            instance.save()

            # Update related order status
            if status == ApplicationStatus.ACCEPTED:
                order.status = OrderStatus.ACCEPTED
                # Add client to VendorClient if not already present
                customer = order.customer
                vendor = order.vendor
                if customer and vendor:
                    VendorClient.objects.get_or_create(
                        vendor=vendor,
                        customer=customer,
                        defaults={
                            "phone_number": order.phone_number,
                            "fullname": order.fullname,
                            "address": order.address,
                            "region": order.region,
                            "district": order.district,
                            "passport": order.passport,
                            "additional_number": order.additional_number,
                        }
                    )
            elif status == ApplicationStatus.REJECTED:
                order.status = OrderStatus.REJECTED
            order.save()

        return instance
