"""
Order models module.
"""
from django.db import models

from apps.orders.models.order import Order
from core.base_models import BaseModel


class ApplicationStatus(models.TextChoices):
    """
    Choices for application status.
    """

    NEW = 'new'
    ACCEPTED = 'accepted'
    REJECTED = 'rejected'


class OrderApplication(BaseModel):
    """
    Model representing order applications.
    """

    order = models.ForeignKey(
        Order, on_delete=models.CASCADE, null=True, default=None
    )
    status = models.CharField(
        max_length=100,
        choices=ApplicationStatus.choices,
        default=ApplicationStatus.NEW
    )

    class Meta:
        """
        Meta options for OrderApplication model.
        """

        verbose_name = 'application'
        verbose_name_plural = 'Applications'
        ordering = ("-created_at",)

    def __str__(self):
        """
        Return a string representation of the application.
        """
        return f"{self.order.customer.fullname} - {self.order.product.title}"
