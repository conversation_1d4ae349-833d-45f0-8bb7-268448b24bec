"""
order comment model
"""
from django.db import models

from apps.orders.models.order import Order
from apps.users.models import Customer
from apps.products.validators import is_valid_rating

from core.base_models import BaseModel


class OrderComment(BaseModel):
    """
    Model representing comments on orders.
    """

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name="comments",
        null=True,
        default=None
    )
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE, null=True, default=None
    )
    text = models.TextField()
    rating = models.FloatField(validators=[is_valid_rating])

    class Meta:
        """Meta options for OrderComment model."""

        ordering = ("-created_at",)
        verbose_name = "order comment"
        verbose_name_plural = "Order comments"
