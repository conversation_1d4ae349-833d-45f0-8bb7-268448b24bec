"""
Notifications app models.
"""
from django.db import models
from core.base_models import BaseModel


class FCMDevice(BaseModel):
    """
    Model for storing FCM device tokens.
    """
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='fcm_devices'
    )
    token = models.CharField(
        max_length=255,
        unique=True,
        help_text="Firebase Cloud Messaging token"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this device is active"
    )

    class Meta:
        """
        The meta class
        """
        verbose_name = "FCM Device"
        verbose_name_plural = "FCM Devices"
        ordering = ['-created_at']

    def __str__(self):
        token_preview = str(self.token)[:10] if self.token else "No token"
        return f"{self.user.username} - {token_preview}..."


class Notification(BaseModel):
    """
    Model for storing notifications.
    """
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='notifications'
    )
    title = models.Char<PERSON>ield(max_length=255)
    body = models.TextField()
    data = models.J<PERSON><PERSON>ield(default=dict, blank=True)
    is_read = models.BooleanField(default=False)
    notification_type = models.CharField(
        max_length=50,
        help_text="Type of notification (e.g., order_accepted, order_rejected)"
    )

    class Meta:
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.title}"
