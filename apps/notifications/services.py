"""
Firebase notification service.
"""
import firebase_admin

from firebase_admin import credentials, messaging, db

from django.conf import settings
from django.contrib.auth import get_user_model


User = get_user_model()

# Initialize Firebase Admin SDK with environment variables
firebase_credentials = {
    "type": "service_account",
    "project_id": settings.FIREBASE_PROJECT_ID,
    "private_key_id": settings.FIREBASE_PRIVATE_KEY_ID,
    "private_key": settings.FIREBASE_PRIVATE_KEY.replace('\\n', '\n'),
    "client_email": settings.FIREBASE_CLIENT_EMAIL,
    "client_id": settings.FIREBASE_CLIENT_ID,
    "auth_uri": settings.FIREBASE_AUTH_URI,
    "token_uri": settings.FIREBASE_TOKEN_URI,
    "auth_provider_x509_cert_url": settings.FIREBASE_AUTH_PROVIDER_CERT_URL,
    "client_x509_cert_url": settings.FIREBASE_CLIENT_CERT_URL
}

cred = credentials.Certificate(firebase_credentials)
firebase_admin.initialize_app(cred, {
    'databaseURL': settings.FIREBASE_DATABASE_URL
})

class FirebaseService:
    """
    Service class for handling Firebase operations.
    """

    def send_notification(self, registration_ids, title, body, data=None):
        """
        Send FCM notification to one or multiple devices.
        Uses `send` for a single token, and `send_multicast` for multiple.
        """
        if not registration_ids:
            return {
                'success_count': 0,
                'failure_count': 0,
                'error': 'No registration_ids provided'
            }

        try:
            if isinstance(registration_ids, (list, tuple)) and len(registration_ids) > 1:
                # Multiple tokens
                message = messaging.MulticastMessage(
                    notification=messaging.Notification(
                        title=title,
                        body=body,
                    ),
                    data=data or {},
                    tokens=registration_ids,
                )
                response = messaging.send_multicast(message)
                return {
                    'success_count': response.success_count,
                    'failure_count': response.failure_count,
                    'responses': response.responses
                }

            # Single token
            token = registration_ids[0] if isinstance(registration_ids, (list, tuple)) else registration_ids
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=body,
                ),
                data=data or {},
                token=token,
            )
            response = messaging.send(message)
            return {
                'success_count': 1,
                'failure_count': 0,
                'message_id': response
            }

        except Exception as e:
            print(f"FirebaseService: Error sending FCM notification: {str(e)}")
            return {
                'success_count': 0,
                'failure_count': len(registration_ids) if isinstance(registration_ids, (list, tuple)) else 1,
                'error': str(e)
            }

    @staticmethod
    def update_notification_counts(
        user_id: str,
        section: str,
        increment: int = 1
    ) -> bool:
        """
        Update notification counts in Firebase Realtime Database.

        Args:
            user_id: The user's ID
            section: The section to update (e.g., 'orders', 'applications')
            increment: The amount to increment the count by (default: 1)

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            ref = db.reference(f'notifications/{user_id}/{section}')
            current_count = ref.get() or 0
            new_count = current_count + increment
            ref.set(new_count)
            return True
        except Exception as e:
            print(f"Error updating notification counts: {str(e)}")
            return False
