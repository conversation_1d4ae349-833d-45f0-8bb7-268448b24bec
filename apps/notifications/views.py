"""
Notifications app views.
"""
from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response

from apps.notifications.services import FirebaseService


class NotificationViewSet(viewsets.ViewSet):
    """
    ViewSet for handling notifications.
    """
    permission_classes = [permissions.IsAuthenticated]

    @action(detail=False, methods=['get'])
    def counts(self, request):
        """
        Get notification counts for the current user.
        """
        counts = FirebaseService.get_notification_counts(str(request.user.id))
        return Response(counts)

    @action(detail=False, methods=['post'])
    def update_token(self, request):
        """
        Update the user's FCM token.
        """
        token = request.data.get('token')
        if not token:
            return Response(
                {'error': 'Token is required'},
                status=400
            )

        # Update the user's FCM token
        request.user.fcm_token = token
        request.user.save()

        return Response({'status': 'success'})
