"""
the consultation views
"""
from rest_framework import viewsets
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from rest_framework.exceptions import PermissionDenied
from django.core.exceptions import ObjectDoesNotExist

from apps.consultation.models import Consultation
from apps.consultation.serializers import ConsultationSerializer
from apps.consultation.permission import IsVendorConsultationOwner
from apps.users.models import UserTypes


class ConsultationViewSet(viewsets.ModelViewSet):
    """
    the consultation viewset
    """
    queryset = Consultation.objects.all().order_by("-created_at")
    serializer_class = ConsultationSerializer
    permission_classes = [IsVendorConsultationOwner]
    http_method_names = ["get", "post"]

    def get_queryset(self):
        user = self.request.user
        try:
            if user.is_authenticated:
                if user.user_type == UserTypes.VENDOR:
                    vendor_user = user.vendor
                    return self.queryset.filter(vendor=vendor_user)
                if user.user_type == UserTypes.ADMIN:
                    return self.queryset
            raise PermissionDenied("You don't have permission to access")
        except ObjectDoesNotExist as ode:
            return Response(
                {"error": "Object not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except PermissionDenied as pd:
            return Response(
                {"error": "Permission denied", "details": str(pd)},
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_permissions(self):
        if self.action == "create":
            return [permissions.AllowAny()]
        return super().get_permissions()
