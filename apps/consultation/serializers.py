"""
consultation serializers
"""
from rest_framework import serializers
from apps.consultation.models import Consultation
from apps.users.validators import is_valid_uzb_phone_number


class ConsultationSerializer(serializers.ModelSerializer):
    """
    the consultation serializer
    """
    region_name = serializers.CharField(source="region.name", read_only=True)
    district_name = serializers.CharField(source="district.name", read_only=True)

    class Meta:
        """
        the meta class
        """
        model = Consultation
        fields = [
            "id",
            "name",
            "phone_number",
            "region",
            "region_name",
            "district",
            "district_name",
            "comment",
            "created_at",
            "updated_at",
        ]

        def validate(self, attrs):
            """
            validaton
            """
            if not is_valid_uzb_phone_number(attrs["phone_number"]):
                raise serializers.ValidationError(
                    {"phone_number": "Phone number is not valid"}
                )
            return attrs
