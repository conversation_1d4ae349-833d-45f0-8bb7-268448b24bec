"""
consultation models
"""
from django.db import models
from core.base_models import BaseModel
from apps.users.validators import is_valid_uzb_phone_number


class Consultation(BaseModel):
    """
    The consulation model
    """
    name = models.Char<PERSON>ield(max_length=255)
    phone_number = models.Char<PERSON>ield(
        max_length=15, validators=[is_valid_uzb_phone_number]
    )
    region = models.ForeignKey(
        "helpers.Region",
        on_delete=models.SET_NULL,
        null=True,
        related_name="consultations",
    )
    district = models.ForeignKey(
        "helpers.District",
        on_delete=models.SET_NULL,
        null=True,
        related_name="consultations",
    )
    comment = models.TextField()

    def __str__(self) -> str:
        """
        return the string representation of the consultation
        """
        return f"{self.name} - {self.phone_number}"

    class Meta:
        """
        the meta class
        """
        verbose_name = "Consultation"
        verbose_name_plural = "Consultations"
        ordering = ("-created_at",)
