"""
consultation permissions
"""
from rest_framework import permissions
from apps.users.models import UserTypes


class IsVendorConsultationOwner(permissions.BasePermission):
    """
    Custom permission to only allow vendor users to view consultations belonging to them.
    """

    def has_permission(self, request, view):
        if request.user.is_authenticated:
            return request.user.user_type in (UserTypes.ADMIN, UserTypes.VENDOR)
        return False

    def has_object_permission(self, request, view, obj):
        return obj.vendor.user == request.user

class IsCourierOrAdmin(permissions.BasePermission):
    """
    Custom permission to only allow vendor users to view consultations belonging to them.
    """

    def has_permission(self, request, view):
        if request.user.is_authenticated:
            return request.user.user_type in (UserTypes.ADMIN, UserTypes.COURIER)
        return False

    def has_object_permission(self, request, view, obj):
        return obj.vendor.user == request.user
