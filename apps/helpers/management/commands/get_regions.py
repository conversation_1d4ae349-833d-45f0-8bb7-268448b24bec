"""
get regions
"""
import json

from apps.helpers.management.commands._base import BaseClass
from apps.helpers.models import Region


class Command(BaseClass):
    """
    the command for getting regions
    """
    def handle(self, *args, **options):
        with open("apps/helpers/management/commands/regions.json", encoding='utf-8') as f:
            regions = json.load(f).get("regions")

        for region in regions:
            Region.objects.get_or_create(id=region.get("id"), name=region.get("name"))

        self.stdout.write(self.style.SUCCESS("Regions created successfully"))
