"""
get districts
"""
import json

from apps.helpers.management.commands._base import BaseClass
from apps.helpers.models import District


class Command(BaseClass):
    """
    the command for getting districts
    """
    def handle(self, *args, **options):
        with open("apps/helpers/management/commands/regions.json", encoding='utf-8') as f:
            districts = json.load(f).get("districts")

        for district in districts:
            District.objects.get_or_create(
                id=district.get("id"),
                name=district.get("name"),
                region_id=district.get("region_id"),
            )

        self.stdout.write(self.style.SUCCESS("Districts created successfully"))
