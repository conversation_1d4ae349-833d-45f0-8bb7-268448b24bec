"""
models
"""
from django.db import models
from core.base_models import BaseModel


class Region(BaseModel):
    """
    the region model
    """
    name = models.CharField(max_length=255)

    def __str__(self) -> str:
        """
        return the string representation of the region
        """
        return f"{self.id} - {self.name}"


class District(BaseModel):
    """
    the district model
    """
    name = models.CharField(max_length=255)
    region = models.ForeignKey(
        Region, on_delete=models.CASCADE, related_name="districts"
    )

    def __str__(self) -> str:
        """
        return the string representation of the district
        """
        return f"{self.name} - {self.region.name}"
