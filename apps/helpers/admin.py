"""
helpers admin
"""
from django.contrib import admin
from unfold.admin import ModelAdmin, TabularInline
from apps.helpers.models import Region, District


class DistrictInline(TabularInline):
    """
    the district inline
    """
    model = District
    readonly_preprocess_fields = {}


@admin.register(Region)
class RegionAdmin(ModelAdmin):
    """
    the region admin
    """
    list_display = ("id", "name")
    inlines = [DistrictInline]
    readonly_preprocess_fields = {}
