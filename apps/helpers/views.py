"""
helpers views
"""
from django.core.exceptions import ObjectDoesNotExist

from rest_framework import viewsets
from rest_framework import permissions
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import action

from apps.helpers.models import Region, District
from apps.helpers.serializers import RegionSerializer, DistrictSerializer


class RegionViewSet(viewsets.ModelViewSet):
    """
    the region viewset
    """
    queryset = Region.objects.all()
    serializer_class = RegionSerializer
    permission_classes = [permissions.AllowAny]
    http_method_names = ["get"]

    @action(detail=True, methods=["get"])
    def districts(self, request, pk):
        """
        get districts
        """
        try:
            region = self.get_object()
            districts = District.objects.filter(region=region)
            serializer = DistrictSerializer(districts, many=True)
            return Response(serializer.data)
        except ObjectDoesNotExist as ode:
            return Response(
                {"error": "Object not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    # @swagger_auto_schema(
    #     manual_parameters=[
    #         openapi.Parameter(
    #             name="district",
    #             in_=openapi.IN_QUERY,
    #             type=openapi.TYPE_INTEGER,
    #             description="Filter vendors by district ID",
    #         ),
    #     ]
    # )
    # @action(detail=True, methods=["get"])
    # def vendors(self, request, pk):
    #     region = self.get_object()
    #     district_id = request.query_params.get("district")
    #     if district_id:
    #         vendors = Vendor.objects.filter(region=region, district=district_id)
    #     else:
    #         vendors = Vendor.objects.filter(region=region)
    #     serializer = VendorSerializer(vendors, many=True)
    #     paginated = self.paginate_queryset(serializer.data)
    #     return self.get_paginated_response(paginated)

    def get_paginated_response(self, data):
        return Response(data)
