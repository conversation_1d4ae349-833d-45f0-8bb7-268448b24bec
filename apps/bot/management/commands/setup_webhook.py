"""
Django management command to set up the Telegram webhook
"""
import logging
from django.core.management.base import BaseCommand
from apps.bot.bot import setup_webhook

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sets up the Telegram bot webhooks for both main and courier bots'

    def handle(self, *args, **options):
        self.stdout.write('Setting up Telegram webhooks...')
        success = setup_webhook()
        if success:
            self.stdout.write(self.style.SUCCESS('Successfully set up webhooks for both bots'))
        else:
            self.stdout.write(self.style.ERROR('Failed to set up webhooks')) 