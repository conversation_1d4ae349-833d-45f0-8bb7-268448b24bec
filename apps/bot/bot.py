"""
Telegram bot implementation
"""
import re
import logging
import telebot
import requests

from django.conf import settings
from django.db import close_old_connections
from telebot.types import WebAppInfo, InlineKeyboardMarkup, InlineKeyboardButton, ReplyKeyboardMarkup, KeyboardButton
from apps.bot.models import TelegramUser
from apps.users.models import Courier, User, UserTypes


# Configure logging
logger = logging.getLogger(__name__)

# Initialize bots
bot = telebot.TeleBot(settings.BOT_TOKEN)
courier_bot = telebot.TeleBot(settings.COURIER_BOT_TOKEN)

@bot.message_handler(commands=['start'])
def send_welcome(message):
    """
    Send welcome message to user
    """
    user = message.from_user
    chat_id = message.chat.id

    # Ensure we have a fresh database connection
    close_old_connections()

    # Save or update TelegramUser
    tg_user, created = TelegramUser.objects.update_or_create(
        chat_id=chat_id,
        defaults={
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_registered': False,
        }
    )

    # Full name for title formatting
    full_name = f"{user.first_name or ''} {user.last_name or ''}".strip()

    # Create inline keyboard with web app button
    keyboard = InlineKeyboardMarkup()
    web_app_button = InlineKeyboardButton(
        text="Menu",
        web_app=WebAppInfo(url=f"{settings.MINI_APP_URL}?chat_id={chat_id}")
    )
    keyboard.add(web_app_button)

    bot.send_message(
        chat_id,
        f"Assalomu alaykum, <b>{full_name}</b>!\n\n"
        "Suvol.uz botiga xush kelibsiz!\n\n"
        "Menuni ochish uchun quyidagi tugmani bosing:",
        reply_markup=keyboard,
        parse_mode="HTML"
    )

@courier_bot.message_handler(commands=['start'])
def courier_welcome(message):
    """
    Send welcome message to courier
    """
    chat_id = message.chat.id

    # Ensure we have a fresh database connection
    close_old_connections()

    # First try to find courier by chat_id
    try:
        user = User.objects.get(chat_id=chat_id, user_type=UserTypes.COURIER)
        courier = Courier.objects.get(user=user)
        
        # Create inline keyboard with web app button
        keyboard = InlineKeyboardMarkup()
        web_app_button = InlineKeyboardButton(
            text="Open Courier App",
            web_app=WebAppInfo(url=f"{settings.COURIER_APP_URL}?chat_id={chat_id}")
        )
        keyboard.add(web_app_button)

        courier_bot.send_message(
            chat_id,
            f"Assalomu alaykum, <b>{courier.fullname}</b>!\n\n"
            "Suvol.uz kurier botiga xush kelibsiz!\n\n"
            "Kurier ilovasini ochish uchun quyidagi tugmani bosing:",
            reply_markup=keyboard,
            parse_mode="HTML"
        )
    except (User.DoesNotExist, Courier.DoesNotExist):
        # If not found, ask for phone number with share contact button
        keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        keyboard.add(KeyboardButton(text="Share Contact", request_contact=True))
        
        courier_bot.send_message(
            chat_id,
            "Iltimos, telefon raqamingizni yuboring:",
            reply_markup=keyboard
        )

@courier_bot.message_handler(content_types=['contact'])
def handle_contact(message):
    """
    Handle contact sharing
    """
    chat_id = message.chat.id
    contact = message.contact
    
    if not contact.phone_number:
        courier_bot.send_message(
            chat_id,
            "Telefon raqam topilmadi. Iltimos, qaytadan urinib ko'ring."
        )
        return

    # Format phone number to match our format (+998XXXXXXXXX)
    phone_number = contact.phone_number
    if not phone_number.startswith('+'):
        phone_number = '+' + phone_number

    try:
        # Try to find user by phone number
        user = User.objects.get(
            phone_number=phone_number,
            user_type=UserTypes.COURIER
        )

        # Check if user is a courier
        try:
            courier = Courier.objects.get(
                user=user,
            )
            
            # Update user's chat_id
            user.chat_id = chat_id
            user.save(update_fields=['chat_id'])

            # Create inline keyboard with web app button
            keyboard = InlineKeyboardMarkup()
            web_app_button = InlineKeyboardButton(
                text="Open Courier App",
                web_app=WebAppInfo(url=f"{settings.COURIER_APP_URL}?chat_id={chat_id}")
            )
            keyboard.add(web_app_button)

            courier_bot.send_message(
                chat_id,
                f"Assalomu alaykum, <b>{courier.fullname}</b>!\n\n"
                "Suvol.uz kurier botiga xush kelibsiz!\n\n"
                "Kurier ilovasini ochish uchun quyidagi tugmani bosing:",
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        except Courier.DoesNotExist:
            courier_bot.send_message(
                chat_id,
                "Siz hali tizimda kurier sifatida ro'yxatdan o'tmagansiz. "
                "Iltimos, avval administrator orqali ro'yxatdan o'ting."
            )
    except User.DoesNotExist:
        courier_bot.send_message(
            chat_id,
            "Siz hali tizimda ro'yxatdan o'tmagansiz. "
            "Iltimos, avval administrator orqali ro'yxatdan o'ting."
        )

    except Exception as e:
        logger.error("ERROR in handle_contact: %s", str(e))
        courier_bot.send_message(
            chat_id,
            "Xatolik yuz berdi. Iltimos, keyinroq urinib ko'ring."
        )

def normalize_phone_number(phone):
    """
    Normalize phone number to +998XXXXXXXXX format
    """
    # Remove all non-digit characters
    digits = re.sub(r'\D', '', phone)

    # Handle different formats
    if digits.startswith('998') and len(digits) == 12:
        return '+' + digits
    elif digits.startswith('8') and len(digits) == 9:
        return '+998' + digits
    elif len(digits) == 9:
        return '+998' + digits
    elif digits.startswith('+998'):
        return phone

    return phone

@courier_bot.message_handler(func=lambda message: True)
def handle_courier_message(message):
    """
    Handle all courier messages
    """
    chat_id = message.chat.id
    text = message.text

    # Normalize the phone number
    normalized_phone = normalize_phone_number(text)

    # Check if this looks like a phone number
    phone_pattern = r'^\+998\d{9}$'
    if re.match(phone_pattern, normalized_phone):
        try:
            # Ensure fresh database connection
            close_old_connections()

            # Try to find courier user by phone number
            courier_users = User.objects.filter(
                phone_number=normalized_phone,
                user_type=UserTypes.COURIER
            )

            if not courier_users.exists():
                courier_bot.send_message(
                    chat_id,
                    f"Telefon raqam {normalized_phone} kurier sifatida tizimda topilmadi. "
                    "Iltimos, avval administrator orqali ro'yxatdan o'ting."
                )
                return

            # If multiple courier users, take the first active one or just the first
            user = courier_users.filter(is_active=True).first() or courier_users.first()

            # Check if user has a courier record
            try:
                courier = Courier.objects.get(user=user)

                # Update user's chat_id
                user.chat_id = chat_id
                user.save(update_fields=['chat_id'])

                # Create inline keyboard with web app button
                keyboard = InlineKeyboardMarkup()
                web_app_button = InlineKeyboardButton(
                    text="Open Courier App",
                    web_app=WebAppInfo(url=f"{settings.COURIER_APP_URL}?chat_id={chat_id}")
                )
                keyboard.add(web_app_button)

                courier_bot.send_message(
                    chat_id,
                    f"Assalomu alaykum, <b>{courier.fullname}</b>!\n\n"
                    "Suvol.uz kurier botiga xush kelibsiz!\n\n"
                    "Kurier ilovasini ochish uchun quyidagi tugmani bosing:",
                    reply_markup=keyboard,
                    parse_mode="HTML"
                )

            except Courier.DoesNotExist:
                courier_bot.send_message(
                    chat_id,
                    "Sizning kurier ma'lumotlaringiz to'liq emas. "
                    "Iltimos, administrator bilan bog'laning."
                )
                return

        except Exception as e:
            logger.error(f"Error processing courier login: {str(e)}")
            courier_bot.send_message(
                chat_id,
                "Xatolik yuz berdi. Iltimos, qaytadan urinib ko'ring."
            )
    else:
        # Not a phone number, show instructions
        keyboard = ReplyKeyboardMarkup(resize_keyboard=True, one_time_keyboard=True)
        keyboard.add(KeyboardButton(text="Share Contact", request_contact=True))

        courier_bot.send_message(
            chat_id,
            "Iltimos, to'g'ri formatda telefon raqamingizni yuboring:\n"
            "Masalan: +998901234567 yoki 901234567\n\n"
            "Yoki quyidagi tugma orqali kontaktingizni ulashing:",
            reply_markup=keyboard
        )

@bot.message_handler(func=lambda message: True)
def echo_all(message):
    """
    Echo all messages
    """
    bot.reply_to(message, f"Echo: {message.text}")

@courier_bot.message_handler(func=lambda message: True)
def courier_echo_all(message):
    """
    Echo all messages for courier bot
    """
    courier_bot.reply_to(message, f"Echo: {message.text}")

def setup_webhook():
    """
    Configure the Telegram bot webhook URLs.
    """
    webhook_url = getattr(settings, 'TELEGRAM_WEBHOOK_URL', None)
    if not webhook_url:
        logger.error("TELEGRAM_WEBHOOK_URL not found in settings!")
        return False

    # Construct the full webhook URLs
    main_webhook_url = f"{webhook_url.rstrip('/')}/telegram/webhook/"
    courier_webhook_url = f"{webhook_url.rstrip('/')}/telegram/courier-webhook/"

    try:
        # Base API URLs
        main_base_api_url = f"https://api.telegram.org/bot{settings.BOT_TOKEN}"
        courier_base_api_url = f"https://api.telegram.org/bot{settings.COURIER_BOT_TOKEN}"

        # Remove any existing webhooks
        for base_url in [main_base_api_url, courier_base_api_url]:
            delete_url = f"{base_url}/deleteWebhook"
            delete_response = requests.get(delete_url, timeout=10)
            delete_result = delete_response.json()

            if not delete_result.get('ok'):
                logger.error("Failed to delete existing webhook: %s", delete_result)
                return False

        # Set new webhooks
        webhook_configs = [
            (main_base_api_url, main_webhook_url),
            (courier_base_api_url, courier_webhook_url)
        ]

        for base_url, webhook_url in webhook_configs:
            set_url = f"{base_url}/setWebhook"
            set_data = {'url': webhook_url}
            set_response = requests.post(set_url, data=set_data, timeout=10)
            set_result = set_response.json()

            if not set_result.get('ok'):
                logger.error("Failed to set webhook: %s", set_result)
                return False

            # Verify webhook is set
            info_url = f"{base_url}/getWebhookInfo"
            info_response = requests.get(info_url, timeout=10)
            info_result = info_response.json()

            if not info_result.get('ok'):
                logger.error("Failed to get webhook info: %s", info_result)
                return False

        return True

    except Exception as e:
        logger.error("ERROR setting webhook: %s", str(e))
        return False

def main():
    """
    Main function to start the bot
    """
    # Ensure we have a fresh database connection before starting
    close_old_connections()

    if getattr(settings, 'USE_WEBHOOK', False):
        setup_webhook()
    else:
        bot.infinity_polling()
        courier_bot.infinity_polling()
