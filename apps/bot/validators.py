"""
bot validators
"""
import hashlib
import hmac
import time
import urllib.parse
from typing import Dict, Any


class TelegramWebAppValidator:
    """
    Validator for Telegram WebApp initialization data.
    """

    @staticmethod
    def validate_init_data(init_data: str, bot_token: str) -> Dict[str, Any]:
        """
        Parse and verify Telegram WebApp initialization data.

        Args:
            init_data: Raw initialization data string from Telegram WebApp
            bot_token: Telegram bot token for verification

        Returns:
            Dictionary of verified data

        Raises:
            ValueError: If validation fails
        """
        # Parse query string into dictionary
        data = dict(urllib.parse.parse_qsl(init_data, strict_parsing=True))

        # Extract and remove hash for verification
        hash_value = data.pop("hash", None)
        if not hash_value:
            raise ValueError("Missing hash in initData")

        # Create data check string as per Telegram docs
        data_check_string = "\n".join(f"{k}={v}" for k, v in sorted(data.items()))

        # Generate secret key using HMAC-SHA256
        secret_key = hmac.new(
            key=bot_token.encode(),
            msg=b"WebAppData",
            digestmod=hashlib.sha256
        ).digest()

        # Calculate expected hash
        expected_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()

        # Verify hash
        if expected_hash != hash_value:
            raise ValueError("initData hash verification failed")

        if int(data.get("auth_date", 0)) < time.time() - 86400:
            raise ValueError("initData has expired (older than 24 hours)")

        return data
