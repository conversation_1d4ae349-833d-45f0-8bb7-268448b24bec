"""
the bot views
"""
import logging

from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db import transaction
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from apps.users.custom_jwt import MyTokenObtainPairSerializer
from apps.bot.models import TelegramUser
from apps.users.models import User
from apps.users.models import UserTypes

# Configure logging
logger = logging.getLogger(__name__)

class MiniAppLoginView(APIView):
    """
    Login endpoint for existing users through Telegram Mini App
    """

    def post(self, request, *args, **kwargs):
        """
        Process login from Telegram Mini App.
        """
        # Get chat_id from query parameter
        chat_id = request.query_params.get("chat_id")

        if not chat_id:
            return Response(
                {"error": "Missing required chat_id parameter"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convert chat_id to integer
            try:
                chat_id = int(chat_id)
            except ValueError:
                return Response(
                    {"error": "Invalid chat_id format"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get phone number and password from request
            phone_number = request.data.get('phone_number')
            password = request.data.get('password')
            
            if not phone_number:
                return Response(
                    {"error": "Phone number is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if not password:
                return Response(
                    {"error": "Password is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            with transaction.atomic():
                # Find user by phone number first
                try:
                    user = User.objects.get(
                        phone_number=phone_number,
                        user_type=UserTypes.CUSTOMER
                    )
                except User.DoesNotExist:
                    logger.warning("User not found with phone number: %s", phone_number)
                    return Response(
                        {"error": "User not found with this phone number"},
                        status=status.HTTP_404_NOT_FOUND
                    )

                # Try to find existing Telegram user with this chat_id
                try:
                    telegram_user = TelegramUser.objects.get(
                        chat_id=chat_id,
                    )

                    if telegram_user.user is None:
                        telegram_user.user = user.customer
                        telegram_user.is_registered = True
                        telegram_user.save(update_fields=['user', 'is_registered'])

                except TelegramUser.DoesNotExist:
                    logger.info("Creating new Telegram user for chat_id: %s", chat_id)
                    # Create new Telegram user if doesn't exist
                    telegram_user = TelegramUser.objects.create(
                        chat_id=chat_id,
                        user=user.customer,
                        is_registered=True
                    )
                
                # Generate tokens
                serializer = MyTokenObtainPairSerializer(data={
                    'username': user.phone_number,
                    'password': password
                })
                serializer.is_valid(raise_exception=True)
                tokens = serializer.validated_data

                return Response({
                    'access': tokens['access'],
                    'refresh': tokens['refresh'],
                    'user_type': user.user_type,
                    'is_registered': True
                })

        except ValidationError as ve:
            logger.error("Validation error: %s", str(ve))
            return Response(
                {"error": "Validation error", "details": str(ve)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except IntegrityError as ie:
            logger.error("Database integrity error: %s", str(ie))
            return Response(
                {"error": "Database error", "details": str(ie)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            logger.error("Unexpected error: %s", str(e))
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
