"""
Webhook view for handling Telegram updates
"""
import json
import logging
import telebot
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from apps.bot.bot import bot, courier_bot

# Configure logging
logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
def webhook(request):
    """
    Handle incoming Telegram webhook requests
    """
    try:
        logger.info("=== Received Telegram Webhook ===")
        logger.info("Request headers: %s", dict(request.headers))

        # Check for empty request
        if not request.body:
            logger.warning("Empty request body")
            return JsonResponse(
                {"error": "Empty request body"},
                status=400
            )

        # Parse the update data
        try:
            update = json.loads(request.body.decode('utf-8'))
            logger.info("Received update: %s", update)
        except json.JSONDecodeError as e:
            logger.error("Invalid JSON: %s", str(e))
            logger.error("Raw request body: %s", request.body)
            return JsonResponse(
                {"error": "Invalid JSON", "details": str(e)},
                status=400
            )

        # Basic validation - check if this looks like a Telegram update
        if not isinstance(update, dict) or 'update_id' not in update:
            logger.warning("Invalid update format - missing update_id. Update: %s", update)
            return JsonResponse(
                {"error": "Invalid update format", "details": "Missing update_id"},
                status=400
            )

        # Process the update through telebot
        try:
            bot.process_new_updates([telebot.types.Update.de_json(update)])
            logger.info("Successfully processed update")
        except Exception as e:
            logger.error("Error processing update: %s", str(e))
            return JsonResponse(
                {"error": "Error processing update", "details": str(e)},
                status=500
            )

        return JsonResponse({"status": "success"}, status=200)

    except Exception as e:
        logger.error("ERROR in webhook: %s", str(e))
        return JsonResponse(
            {"error": "An unexpected error occurred", "details": str(e)},
            status=500
        )

@csrf_exempt
@require_http_methods(["POST"])
def courier_webhook(request):
    """
    Handle incoming Telegram webhook requests for courier bot
    """
    try:
        logger.info("=== Received Courier Telegram Webhook ===")
        logger.info("Request headers: %s", dict(request.headers))

        # Check for empty request
        if not request.body:
            logger.warning("Empty request body")
            return JsonResponse(
                {"error": "Empty request body"},
                status=400
            )

        # Parse the update data
        try:
            update = json.loads(request.body.decode('utf-8'))
            logger.info("Received update: %s", update)
        except json.JSONDecodeError as e:
            logger.error("Invalid JSON: %s", str(e))
            logger.error("Raw request body: %s", request.body)
            return JsonResponse(
                {"error": "Invalid JSON", "details": str(e)},
                status=400
            )

        # Basic validation - check if this looks like a Telegram update
        if not isinstance(update, dict) or 'update_id' not in update:
            logger.warning("Invalid update format - missing update_id. Update: %s", update)
            return JsonResponse(
                {"error": "Invalid update format", "details": "Missing update_id"},
                status=400
            )

        # Process the update through courier bot
        try:
            courier_bot.process_new_updates([telebot.types.Update.de_json(update)])
            logger.info("Successfully processed courier update")
        except Exception as e:
            logger.error("Error processing courier update: %s", str(e))
            return JsonResponse(
                {"error": "Error processing update", "details": str(e)},
                status=500
            )

        return JsonResponse({"status": "success"}, status=200)

    except Exception as e:
        logger.error("ERROR in courier webhook: %s", str(e))
        return JsonResponse(
            {"error": "An unexpected error occurred", "details": str(e)},
            status=500
        )
