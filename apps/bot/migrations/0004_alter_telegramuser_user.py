# Generated by Django 4.2.10 on 2025-06-01 12:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
        ('bot', '0003_telegramuser_is_registered'),
    ]

    operations = [
        # First, drop the foreign key constraint
        migrations.RunSQL(
            sql='ALTER TABLE tg_users DROP CONSTRAINT IF EXISTS tg_users_user_id_fkey;',
            reverse_sql='',
        ),
        # Then, drop the column
        migrations.RunSQL(
            sql='ALTER TABLE tg_users DROP COLUMN IF EXISTS user_id;',
            reverse_sql='',
        ),
        # Add the new column with correct type
        migrations.AddField(
            model_name='telegramuser',
            name='user',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='users.customer'
            ),
        ),
    ]
