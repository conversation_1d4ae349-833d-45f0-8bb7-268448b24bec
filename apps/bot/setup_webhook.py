"""
<PERSON><PERSON><PERSON> to manually set up the Telegram bot webhook.
"""
import logging
import requests
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

def setup_webhook():
    """
    Set up the Telegram webhook using settings from Django settings
    """
    # Get bot token from settings
    token = getattr(settings, 'BOT_TOKEN', None)
    if not token:
        logger.error("BOT_TOKEN not found in settings")
        return False

    # Get webhook base URL from settings
    webhook_base = getattr(settings, 'TELEGRAM_WEBHOOK_URL', None)
    if not webhook_base:
        logger.error("TELEGRAM_WEBHOOK_URL not found in settings")
        return False

    # Construct full webhook URL
    webhook_url = f"{webhook_base.rstrip('/')}/telegram/webhook/"
    logger.info("Setting webhook URL to: %s", webhook_url)

    # API URLs
    base_api_url = f"https://api.telegram.org/bot{token}"
    delete_url = f"{base_api_url}/deleteWebhook"
    set_url = f"{base_api_url}/setWebhook"
    info_url = f"{base_api_url}/getWebhookInfo"

    try:
        # Delete existing webhook
        logger.info("Deleting existing webhook...")
        delete_response = requests.get(delete_url, timeout=10)
        delete_result = delete_response.json()

        if not delete_result.get('ok'):
            logger.error("Failed to delete existing webhook: %s", delete_result)
            return False

        logger.info("Existing webhook deleted successfully")

        # Set new webhook
        logger.info("Setting new webhook to %s...", webhook_url)
        set_response = requests.post(set_url, data={'url': webhook_url}, timeout=10)
        set_result = set_response.json()

        if not set_result.get('ok'):
            logger.error("Failed to set webhook: %s", set_result)
            return False

        logger.info("Webhook set successfully")

        # Get webhook info to verify
        logger.info("Getting webhook info to verify...")
        info_response = requests.get(info_url, timeout=10)
        info_result = info_response.json()

        if info_result.get('ok'):
            webhook_info = info_result.get('result', {})
            logger.info("Webhook URL: %s", webhook_info.get('url'))
            logger.info("Has custom certificate: %s", webhook_info.get('has_custom_certificate'))
            logger.info("Pending update count: %s", webhook_info.get('pending_update_count'))
            if webhook_info.get('last_error_date'):
                logger.warning("Last error date: %s", webhook_info.get('last_error_date'))
                logger.warning("Last error message: %s", webhook_info.get('last_error_message'))
            logger.info("Max connections: %s", webhook_info.get('max_connections'))
            return True
        else:
            logger.error("Failed to get webhook info: %s", info_result)
            return False

    except (requests.RequestException, ValueError) as e:
        logger.error("Error setting up webhook: %s", str(e))
        return False


if __name__ == "__main__":
    # Allow this script to be run standalone
    import os
    import sys
    import django
    from configurations.importer import install

    # Add the project root directory to sys.path
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
    sys.path.insert(0, project_root)

    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.config.local')
    os.environ.setdefault('DJANGO_CONFIGURATION', 'Local')

    # Install the configurations importer
    install()

    # Initialize Django
    django.setup()

    # Run webhook setup
    SUCCESS = setup_webhook()
    if SUCCESS:
        logger.info("Webhook setup completed successfully")
    else:
        logger.error("Webhook setup failed")
