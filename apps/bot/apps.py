"""
Bot app configuration
"""
import logging
import sys
from django.apps import AppConfig

logger = logging.getLogger(__name__)

class BotConfig(AppConfig):
    """Bot app configuration class"""
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.bot'

    def ready(self):
        """
        Run when Django starts - set up the webhook here
        but only when using the runserver command
        """
        if 'runserver' in sys.argv:
            # Import here to avoid circular imports
            from django.conf import settings

            if getattr(settings, 'USE_WEBHOOK', False):
                logger.info("Setting up Telegram webhook on server startup")
                # Import webhook setup function
                from .bot import setup_webhook

                # Run setup
                try:
                    success = setup_webhook()
                    if success:
                        logger.info("Telegram webhook configured successfully")
                    else:
                        logger.error("Failed to configure Telegram webhook")
                except Exception as e:
                    logger.error("Error setting up webhook: %s", str(e))
            else:
                logger.info("Webhook mode is disabled, skipping webhook setup")
