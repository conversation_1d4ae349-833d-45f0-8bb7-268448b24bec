"""
telegram user models
"""
from django.db import models
from core.base_models import BaseModel

from apps.users.models import Customer


class TelegramUser(BaseModel):
    """
    Model to link Telegram users with Django User accounts
    """
    user = models.OneToOneField(Customer, on_delete=models.CASCADE, null=True, blank=True)

    chat_id = models.BigIntegerField(unique=True, primary_key=True)
    username = models.CharField(max_length=255, blank=True, null=True)
    first_name = models.CharField(max_length=255, blank=True, null=True)
    last_name = models.CharField(max_length=255, blank=True, null=True)

    is_registered = models.BooleanField(default=False)

    class Meta:
        """
        the meta class
        """
        db_table = 'tg_users'
        verbose_name = 'Telegram User'
        verbose_name_plural = 'Telegram Users'

    def __str__(self):
        if self.username:
            return f"@{self.username} ({self.chat_id})"
        return f"User {self.chat_id}"
