"""
the product views
"""
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.db import IntegrityError
from rest_framework import serializers

from apps.products.filters.product import ProductFilter
from apps.products.models import Product, ProductComment
from apps.products.permissions import IsVendorProductOwner
from apps.products.serializers import ProductCommentSerializer, ProductSerializer
from apps.users.models import UserTypes


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the Product model.
    """

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsVendorProductOwner]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ProductFilter

    def get_queryset(self):
        """
        Filter products based on user type and permissions.
        """
        queryset = super().get_queryset()
        user = self.request.user

        if not user.is_authenticated:
            # For unauthenticated users, show only active products
            return queryset.filter(vendor__user__is_active=True)

        if user.user_type == UserTypes.VENDOR:
            # Vendors can only see their own products
            return queryset.filter(vendor=user.vendor)
        elif user.user_type == UserTypes.ADMIN:
            # Admins can see all products
            return queryset
        elif user.user_type == UserTypes.CUSTOMER:
            # Customers can see all active products
            return queryset.filter(vendor__user__is_active=True)
        else:
            # Other users (couriers) can only see active products
            return queryset.filter(vendor__user__is_active=True)

    def get_permissions(self):
        """
        Return permission classes based on the action.
        Allow any user to list or retrieve products.
        """
        if self.action in ["list", "retrieve"]:
            return [permissions.AllowAny()]
        return super().get_permissions()

    def get_serializer_class(self):
        """
        Return appropriate serializer class based on the action.
        """
        if self.action == "comments":
            return ProductCommentSerializer
        return ProductSerializer

    def create(self, request, *args, **kwargs):
        """
        Create a new product with improved error handling
        """
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            response_data = {
                "data": serializer.data
            }
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid product data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while creating the product",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        """
        Update a product with improved error handling
        """
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return Response(
                serializer.data,
                status=status.HTTP_200_OK
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid product data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while updating the product",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def destroy(self, request, *args, **kwargs):
        """
        Delete a product with improved error handling
        """
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            response_data = {
                "message": "Product deleted successfully"
            }
            return Response(
                response_data,
                status=status.HTTP_204_NO_CONTENT
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while deleting the product",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def comments(self, request, pk=None):
        """
        Add a comment to a product with improved error handling
        """
        try:
            product = self.get_object()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save(product=product, user=request.user)
            response_data = {
                "message": "Comment added successfully",
                "data": serializer.data
            }
            return Response(
                response_data,
                status=status.HTTP_201_CREATED
            )
        except serializers.ValidationError as e:
            return Response(
                {
                    "status": "error",
                    "message": "Invalid comment data provided",
                    "details": e.detail
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "message": "An unexpected error occurred while adding the comment",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(
        detail=False,
        methods=["get"],
        url_path="all-comments"
    )
    def all_comments(self, request):
        """
        List all product comments ordered by creation date.
        """
        comments = ProductComment.objects.all().order_by("-created_at")
        serializer = ProductCommentSerializer(comments, many=True)
        return Response(serializer.data)

    @action(
        detail=True,
        methods=["get", "post"],
        permission_classes=[permissions.AllowAny],
    )
    def comments(self, request, pk):
        """
        Get or add comments for a specific product.
        """
        try:
            product = self.get_object()

            if request.method == "POST":
                serializer = ProductCommentSerializer(
                    data=request.data, 
                    context={"request": request, "product": product}
                )
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(serializer.data, status=status.HTTP_201_CREATED)

            comments = ProductComment.objects.filter(product=product)
            serializer = ProductCommentSerializer(
                comments, many=True, context={"request": request}
            )

            pagination = self.paginate_queryset(serializer.data)
            if pagination:
                return self.get_paginated_response(pagination)

            return Response(serializer.data)
        except ValidationError as ve:
            return Response(
                {"error": "Validation error", "details": str(ve)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except ObjectDoesNotExist as ode:
            return Response(
                {"error": "Product or comment not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except IntegrityError as ie:
            return Response(
                {"error": "Database error", "details": str(ie)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
