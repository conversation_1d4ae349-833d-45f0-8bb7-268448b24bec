"""
the banner views
"""
from django_filters.rest_framework import DjangoFilt<PERSON><PERSON><PERSON><PERSON>
from rest_framework import permissions, viewsets, status
from rest_framework.response import Response
from django.core.exceptions import ObjectDoesNotExist

from apps.products.models.banner import Banner
from apps.products.serializers.banner import BannerSerializer


class BannerViewSet(viewsets.ModelViewSet):
    """ViewSet for the Banner model."""

    queryset = Banner.objects.all().order_by('position')
    serializer_class = BannerSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['name', 'position']

    def get_permissions(self):
        """
        Return permission classes based on the action.
        Allow any user to list or retrieve banners, require admin for other actions.
        """
        if self.action in ["list", "retrieve"]:
            return [permissions.AllowAny()]
        # Assuming admin operations for create, update, delete
        return [permissions.IsAdminUser()]

    def retrieve(self, request, pk=None):
        """
        Retrieve a banner by id or external_id.
        """
        try:
            banner = self.get_object()
        except Banner.DoesNotExist:
            try:
                banner = Banner.get_by_external_id(pk)
            except Banner.DoesNotExist:
                return Response(
                    {"error": "Banner not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        except ObjectDoesNotExist as ode:
            return Response(
                {"error": "Object not found", "details": str(ode)},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": "An unexpected error occurred", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        serializer = self.get_serializer(banner)
        return Response(serializer.data)
