"""
The Product serializer
"""
from rest_framework import serializers

from apps.orders.models import Order
from apps.products.models.product import Product, ProductComment
from apps.users.customers.serializers import CustomerSerializer
from apps.users.models import UserTypes, Vendor


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product model.
    """
    rating = serializers.ReadOnlyField()
    vendor = serializers.PrimaryKeyRelatedField(queryset=Vendor.objects.all())

    def get_queryset(self):
        """
        get the queryset
        """
        queryset = super().get_queryset()
        vendor_id = self.request.query_params.get("vendor_id")
        user = self.request.user
        if vendor_id and user.is_authenticated and user.user_type == UserTypes.ADMIN:
            queryset = queryset.filter(vendor_id=vendor_id)
        return queryset

    def validate_vendor(self, value):
        """
        validate the vendor
        """
        request = self.context.get("request")
        user = request.user
        if user.user_type == UserTypes.VENDOR:
            # Vendor users cannot set vendor field manually
            if value != user.vendor:
                raise serializers.ValidationError("You cannot assign a different vendor.")
        elif user.user_type == UserTypes.ADMIN:
            # Admin users must provide a valid vendor
            if not value:
                raise serializers.ValidationError("Vendor must be provided.")
        else:
            raise serializers.ValidationError("You do not have permission to add product.")
        return value

    def create(self, validated_data):
        """
        create a new product instance
        """
        request = self.context.get("request")
        user = request.user
        try:
            if user.user_type == UserTypes.VENDOR:
                validated_data["vendor"] = user.vendor
            elif user.user_type == UserTypes.ADMIN:
                # vendor should be provided in validated_data by admin
                if "vendor" not in validated_data or validated_data["vendor"] is None:
                    raise serializers.ValidationError({"vendor": "Vendor must be provided."})
            else:
                raise serializers.ValidationError(
                    {"message": "You do not have permission to add product"}
                )
            return super().create(validated_data)
        except Vendor.DoesNotExist as exc:
            raise serializers.ValidationError(
                {"message": "You do not have permission to add product"}
            ) from exc
        except Exception as exc:
            raise serializers.ValidationError(
                {"message": "You do not have permission to add product"}
            ) from exc

    class Meta:
        """
        Meta class for ProductSerializer.
        """
        model = Product
        fields = "__all__"
        depth = 2
        ref_name = "ProductSerializer"


class ProductCommentSerializer(serializers.ModelSerializer):
    """
    Serializer for ProductComment model.
    """

    customer = CustomerSerializer(read_only=True)
    product = serializers.StringRelatedField()

    class Meta:
        """
        Meta class for ProductCommentSerializer.
        """
        model = ProductComment
        fields = "__all__"
        depth = 2

    def validate(self, attrs):
        """
        Validate the comment data
        """
        product = self.context["product"]
        customer = self.context["request"].user.customer

        is_product_ordered = Order.objects.filter(
            product=product, customer=customer
        ).exists()

        if not is_product_ordered:
            raise serializers.ValidationError(
                {"message": "You should order before commenting"}
            )

        if ProductComment.objects.filter(customer=customer, product=product).exists():
            raise serializers.ValidationError(
                {"message": "You already commented it"}
            )

        return attrs

    def create(self, validated_data):
        """
        Create a new product comment instance.
        """
        validated_data["customer"] = self.context["request"].user.customer
        validated_data["product"] = self.context["product"]
        return super().create(validated_data)
