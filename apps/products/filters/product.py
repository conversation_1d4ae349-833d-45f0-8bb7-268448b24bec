"""
the product filters
"""
import django_filters
from apps.products.models import Product


class ProductFilter(django_filters.FilterSet):
    """
    the product filter
    """
    vendor_region = django_filters.NumberFilter(field_name="vendor__places_available__region__id")
    vendor_district = django_filters.NumberFilter(field_name="vendor__places_available__districts__id")
    vendor_id = django_filters.NumberFilter(field_name="vendor__id")
    order_by_rating = django_filters.ChoiceFilter(
        choices=(("asc", "Ascending"), ("desc", "Descending")),
        method="filter_by_rating"
    )

    class Meta:
        """
        the meta field
        """
        model = Product
        fields = ["order_by_rating", "vendor_region", "vendor_district", "vendor_id"]

    def filter_by_rating(self, queryset, name, value):
        if value == "asc":
            return queryset.order_by("rating")
        elif value == "desc":
            return queryset.order_by("-rating")
        return queryset
