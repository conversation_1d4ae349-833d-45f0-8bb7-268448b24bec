# Generated by Django 4.0.6 on 2025-04-23 13:47

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0003_alter_product_vendor_alter_productcomment_customer_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('image_url', models.ImageField(null=True, upload_to='banners', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpeg', 'jpg', 'png', 'webp'])])),
                ('ad_link', models.URLField(blank=True, null=True)),
                ('position', models.IntegerField(unique=True)),
            ],
            options={
                'db_table': 'banner',
                'ordering': ['id'],
            },
        ),
    ]
