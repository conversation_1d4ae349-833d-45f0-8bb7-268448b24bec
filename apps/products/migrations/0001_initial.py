# Generated by Django 5.2 on 2025-04-09 18:09

import apps.products.validators
import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False, unique=True)),
                ('title', models.CharField(max_length=250)),
                ('capacity', models.IntegerField()),
                ('image', models.ImageField(null=True, upload_to='products', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpeg', 'jpg', 'png', 'webp'])])),
                ('description', models.TextField()),
                ('price', models.IntegerField()),
                ('rating', models.FloatField(default=0.0)),
            ],
            options={
                'verbose_name': 'product',
                'verbose_name_plural': 'Products',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='ProductComment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('text', models.TextField()),
                ('rating', models.FloatField(validators=[apps.products.validators.is_valid_rating])),
            ],
            options={
                'verbose_name': 'product comment',
                'verbose_name_plural': 'Product comments',
                'ordering': ('-created_at',),
            },
        ),
    ]
