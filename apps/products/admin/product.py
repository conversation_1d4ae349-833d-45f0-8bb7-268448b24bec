"""
the product admin
"""
from django.contrib import admin
from unfold.admin import ModelAdmin
from apps.products.models import Product, ProductComment


@admin.register(Product)
class ProductAdmin(ModelAdmin):
    """
    Admin interface configuration for the Product model.
    """
    list_display = (
        "title",
        "vendor",
        "capacity",
        "price",
        "rating",
        "avg_rating"
    )

    list_filter = ("created_at",)
    search_fields = ("title", "vendor")
    readonly_fields = (
        "rating",
    )
    readonly_preprocess_fields = {}


@admin.register(ProductComment)
class ProductCommentAdmin(ModelAdmin):
    """
    Admin interface configuration for the ProductComment model.
    """
    list_display = (
        "product",
        "customer",
        "text",
        "rating",
    )

    list_filter = ("created_at",)
    search_fields = ("product", "customer")
    readonly_fields = (
        "id",
        "product",
        "customer",
        "text",
        "rating",
    )
    readonly_preprocess_fields = {}
