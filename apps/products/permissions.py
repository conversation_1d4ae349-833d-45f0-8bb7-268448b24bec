"""
product permissions
"""
from rest_framework import permissions
from apps.users.models import UserTypes


class IsVendorProductOwner(permissions.BasePermission):
    """
    Custom permission to only allow vendor users to view products belonging to them,
    and allow admin users full access.
    """

    def has_permission(self, request, view):
        if request.user.is_authenticated:
            return request.user.user_type in (UserTypes.ADMIN, UserTypes.VENDOR)
        return False

    def has_object_permission(self, request, view, obj):
        if request.user.user_type == UserTypes.ADMIN:
            return True
        return obj.vendor.user == request.user
