
from django.utils import timezone
import json
from typing import Dict


def dict_to_str(context: Dict) -> str:
    res = list(filter(lambda x:x, context.values()))
    return ", ".join(res)


def time_printer(func):
    def wrapper(*args, **kwargs):
        start = timezone.datetime.now()
        result = func(*args, **kwargs)
        end = timezone.datetime.now()
        print(f'\n{func.__name__} execution time: {end - start}\n')
        return result
    return wrapper

