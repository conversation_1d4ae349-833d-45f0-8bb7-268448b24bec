variables:
  DOCKERFILE: Dockerfile
  TAG: $CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA-$(date +%s)
  PROJECT_NAME: ${CI_PROJECT_NAME}
  DOCKER_DRIVER: overlay2
  DOCKER_BUILDKIT: 1

image: docker:20.10-dind
services:
  - docker:20.10-dind

stages:
  - build-push
  - deploy

# ----------------- Templates ------------------

.ansible_template:
  before_script:
    - |-
      if [[ $CI_COMMIT_REF_NAME == 'staging' ]]; then
        SERVICE_BASE_DIR=water_staging
        DOCKER_COMPOSE=docker-compose.staging.yml
      elif [[ $CI_COMMIT_REF_NAME == 'main' ]]; then
        SERVICE_BASE_DIR=water_prod
        DOCKER_COMPOSE=docker-compose.yml
      fi
    - echo $SSH_KEY_BASE64 |base64 -d > id_rsa
    - chmod 400 id_rsa
    - |-
      cat <<EOF >ansible.cfg
      [defaults]
      host_key_checking = False
      EOF
    - |-
      cat <<EOF >host
      [app]
      $HOST  ansible_user=$USER ansible_port=$PORT
      EOF
    - |-
      cat <<EOF >deploy.yml
      ---
      - name: Deploy
        hosts: all
        become: yes
        become_user: root
        tasks:
          - name: Login to registry
            shell:
              cmd: docker login {{ docker_registry }} -u {{ docker_username }} -p {{ docker_pass }}

          - name: Pull new image
            shell:
              cmd: docker pull {{ docker_image }}:{{ docker_image_tag }}

          - name: Logout from registry
            shell:
              cmd: docker logout {{ docker_registry }}

          - name: Create directory if it doesn't exist
            ansible.builtin.file:
              path: /var/www/{{service_base_dir}}/{{ service_name }}/
              state: directory
              mode: '0755'

          - name: Check if docker-compose.yml exists
            stat:
              path: /var/www/{{service_base_dir}}/{{ service_name }}/docker-compose.yml
            register: compose_file

          - name: Stop old container
            shell:
              cmd: docker-compose down
            args:
              chdir: /var/www/{{service_base_dir}}/{{ service_name }}/
            when: compose_file.stat.exists

          - name: Copy new/replase docker-compose
            copy:
              src: "{{docker_compose}}"
              dest: /var/www/{{service_base_dir}}/{{ service_name }}/docker-compose.yml
              backup: false

          - name: Replace image tag
            replace:
              path: /var/www/{{service_base_dir}}/{{ service_name }}/docker-compose.yml
              regexp: "{{ docker_image }}.*"
              replace: "{{ docker_image }}:{{ docker_image_tag }}"

          - name: Restart docker compose
            shell:
              cmd: docker-compose up --force-recreate  -d
            args:
              chdir: /var/www/{{service_base_dir}}/{{ service_name }}/

          - name: Wait service 10s
            shell:
              cmd: sleep 10
            args:
              chdir: /var/www/{{service_base_dir}}/{{ service_name }}/

          - name: Change to the directory with docker-compose.yml
            command: chdir=/var/www/{{service_base_dir}}/{{ service_name }}/ docker-compose ps
            register: compose_ps_output

          - name: Print docker-compose ps output
            debug:
              var: compose_ps_output.stdout

          - name: Check if services are running
            assert:
              that:
                - "'Up' in compose_ps_output.stdout"
              fail_msg: "One or more services are not running."
              success_msg: "All services are running."
      EOF

# ----------------- END Templates -----------------

build-push:
  stage: build-push
  timeout: 30m
  only:
    - main
    - staging
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - |-
      if [[ $CI_COMMIT_REF_NAME == 'staging' ]]; then
        TAG=staging-$CI_COMMIT_SHORT_SHA-$(date +%s)
      elif [[ $CI_COMMIT_REF_NAME == 'main' ]]; then
        TAG=main-$CI_COMMIT_SHORT_SHA-$(date +%s)
      fi
    - echo -e "------ Image name is $CI_REGISTRY_IMAGE:$TAG ------"
    - echo TAG=$TAG > job_vars
    - echo CI_REGISTRY_IMAGE=$CI_REGISTRY_IMAGE >> job_vars
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$TAG --file $DOCKERFILE --build-arg CI_JOB_TOKEN=$CI_JOB_TOKEN --build-arg CI_REGISTRY=$CI_REGISTRY .
    - docker push $CI_REGISTRY_IMAGE:$TAG
    - docker logout $CI_REGISTRY
  artifacts:
    paths:
      - job_vars
    expire_in: 5 mins

deploy:
  image: alpinelinux/ansible:latest
  stage: deploy
  timeout: 30m
  variables:
    ANSIBLE_CONFIG: "ansible.cfg"
  only:
    - main
    - staging
  needs:
    - build-push
  extends: .ansible_template
  script:
    - source job_vars
    - echo $CI_PROJECT_NAME
    - echo $TAG
    - ls -la
    - ansible-playbook -vv -i host deploy.yml --key-file id_rsa
      -e "docker_registry=$CI_REGISTRY docker_username=$CI_REGISTRY_USER docker_pass=$CI_JOB_TOKEN"
      -e "docker_image=$CI_REGISTRY_IMAGE docker_image_tag=$TAG"
      -e "service_name=$CI_PROJECT_NAME"
      -e "service_base_dir=$SERVICE_BASE_DIR"
      -e "docker_compose=$DOCKER_COMPOSE"

