services:
  water-back-v2-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: water-back-v2-dev
    volumes:
      - .:/application
    ports:
      - "127.0.0.1:8000:8000"
    networks:
      - water-back-v2
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    command: >
      sh -c "python manage.py migrate && python manage.py collectstatic --noinput && uvicorn config.asgi:application --host 0.0.0.0 --port 8000 --reload"

  postgres:
    image: postgres:13
    container_name: water-back-postgres-dev
    environment:
      - POSTGRES_DB=water_delivery
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=anvarbek082
    ports:
      - "127.0.0.1:5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - water-back-v2

  redis:
    image: redis:6
    container_name: water-back-redis-dev
    environment:
      - REDIS_URL=redis://redis:6379
    ports:
      - "127.0.0.1:6380:6379"
    networks:
      - water-back-v2

networks:
  water-back-v2:
    driver: bridge

volumes:
  postgres_data:
