version: "3"
services:
  water-back-v2-staging:
    image: registry.gitlab.com/servisoft.uz/water-back-v2:latest
    container_name: water-back-v2-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - |
        python3 manage.py migrate &&
        python3 manage.py collectstatic --noinput &&
        python3 manage.py setup_webhook &&
        newrelic-admin run-program gunicorn --bind 0.0.0.0:8000 --access-logfile - config.wsgi:application
    ports:
      - "11007:8000"
    networks:
      - water-back-v2-staging
    env_file:
      - .env
    depends_on:
      - redis

  redis:
    image: redis:6
    container_name: water-back-redis-staging
    ports:
      - "127.0.0.1:6377:6379"
    networks:
      - water-back-v2-staging

networks:
  water-back-v2-staging:
    driver: bridge
