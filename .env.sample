# Database Configuration
DATABASE_URL=*************************************************

# Django settings
DJANGO_DEBUG=True
DJANGO_SECRET_KEY=your_secret_key_here

ALLOWED_HOSTS=*,*.ngrok-free.app,*.ngrok.io
CSRF_TRUSTED_ORIGINS=https://suvol.uz,https://api.suvol.uz,https://*.ngrok-free.app,https://*.ngrok.io

BOT_TOKEN=your_bot_token
COURIER_BOT_TOKEN=your_courier_bot_token
COURIER_APP_URL=https://test.suvol.uz/uz/courierapp
MINI_APP_URL=https://suvol.uz/uz/miniapp
USE_WEBHOOK=True
TELEGRAM_WEBHOOK_URL=https://api.suvol.uz

# SMS Service
ESKIZ_EMAIL=your_eskiz_email
ESKIZ_PASSWORD=your_eskiz_password

# MinIO Storage
MINIO_ENDPOINT=localhost:9000
MINIO_BUCKET_NAME=your_bucket_name
MINIO_ACCESS_KEY=your_access_key
MINIO_SECRET_KEY=your_secret_key
MINIO_SECURE=True
MINIO_QUERYSTRING_AUTH=True
MINIO_STORAGE_MEDIA_URL=your_media_url

# Firebase Cloud Messaging
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=
FIREBASE_CLIENT_ID=
FIREBASE_AUTH_URI=
FIREBASE_TOKEN_URI=
FIREBASE_AUTH_PROVIDER_CERT_URL=
FIREBASE_CLIENT_CERT_URL=
FIREBASE_DATABASE_URL=

# Django Configuration
DJANGO_CONFIGURATION=Local  # Options: Local, Production

# Redis
DJANGO_REDIS=redis://localhost:6380