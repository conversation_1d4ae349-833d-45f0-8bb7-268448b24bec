---
- name: Deploy
  hosts: all
  become: yes
  become_user: root
  tasks:
    - name: Login to registry
      shell:
        cmd: docker login registry.gitlab.com -u {{ docker_username }} -p {{ docker_pass }}

    - name: Replace Image tag
      ansible.builtin.replace:
        path: /var/www/water-back/docker-compose.yaml
        regexp: 'registry.gitlab.com/servisoft.uz/water-back.*-.*'
        replace: "{{image_name}}"

    - name: Pull Image
      shell:
        cmd: docker-compose pull
      args:
        chdir: /var/www/water-back/

    # - name: Run migrations
    #   shell:
    #     cmd: docker run -it --env-file ./.env  --net=host {{image_name}} make migrate
    #   args:
    #     chdir: /var/www/water-back/

    - name: Restart docker compose
      shell:
        cmd: docker-compose up --force-recreate  -d
      args:
        chdir: /var/www/water-back/

    - name: Logout from registry
      shell:
        cmd: docker logout registry.gitlab.com
